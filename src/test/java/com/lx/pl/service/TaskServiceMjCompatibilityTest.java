package com.lx.pl.service;

import com.lx.pl.dto.TaskProcessMessage;
import com.lx.pl.enums.PromptStatus;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.Arrays;
import java.util.List;

/**
 * TaskService MJ兼容性测试
 * 测试batchProcessTask方法对MJ jobId的支持
 * 新实现：MJ任务状态通过Redis管理，不直接查询MJ API
 */
@SpringBootTest
@ActiveProfiles("test")
public class TaskServiceMjCompatibilityTest {

    @Autowired
    private TaskService taskService;

    /**
     * 测试批量处理任务 - 混合markId和jobId
     * 注意：这个测试需要Redis中有相应的测试数据
     */
    @Test
    void testBatchProcessTaskWithMixedIds() {
        try {
            // 准备测试数据：混合传统markId和MJ jobId
            List<String> taskIds = Arrays.asList(
                    "1234567890123456789", // 传统markId（数字格式）
                    "test-mj-job-id-uuid-format", // MJ jobId（UUID格式）
                    "9876543210987654321" // 另一个传统markId
            );

            // 调用批量处理方法
            List<TaskProcessMessage> results = taskService.batchProcessTask(taskIds);

            // 验证结果
            assert results != null;
            assert results.size() == taskIds.size();

            // 打印结果用于调试
            for (int i = 0; i < results.size(); i++) {
                TaskProcessMessage result = results.get(i);
                String inputId = taskIds.get(i);

                System.out.println("输入ID: " + inputId);
                System.out.println("返回markId: " + result.getMarkId());
                System.out.println("返回promptId: " + result.getPromptId());
                System.out.println("状态: " + result.getStatus());
                System.out.println("Index: " + result.getIndex());
                System.out.println("---");

                // 基本验证
                assert result.getMarkId() != null;
                assert result.getStatus() != null;
            }

            System.out.println("批量处理测试完成，处理了 " + results.size() + " 个任务");

        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试单个MJ任务处理
     */
    @Test
    void testProcessSingleMjTask() {
        try {
            String mjJobId = "test-mj-job-id";

            TaskProcessMessage result = taskService.processTask(mjJobId);

            assert result != null;
            System.out.println("MJ任务处理结果:");
            System.out.println("markId: " + result.getMarkId());
            System.out.println("promptId: " + result.getPromptId());
            System.out.println("状态: " + result.getStatus());

        } catch (Exception e) {
            System.err.println("MJ任务测试失败: " + e.getMessage());
            // 这是预期的，因为测试环境可能没有真实的MJ任务数据
        }
    }

    /**
     * 测试传统markId处理（确保向后兼容）
     */
    @Test
    void testProcessTraditionalMarkId() {
        try {
            String markId = "1234567890123456789";

            TaskProcessMessage result = taskService.processTask(markId);

            assert result != null;
            System.out.println("传统markId处理结果:");
            System.out.println("markId: " + result.getMarkId());
            System.out.println("状态: " + result.getStatus());

        } catch (Exception e) {
            System.err.println("传统markId测试失败: " + e.getMessage());
            // 这是预期的，因为测试环境可能没有真实的任务数据
        }
    }
}
