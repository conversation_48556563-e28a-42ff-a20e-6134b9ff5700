package com.lx.pl.service;

import com.lx.pl.config.MidjourneyConfig;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.dto.midjourney.MidjourneyResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * Midjourney服务测试类
 *
 * <AUTHOR>
 */
@SpringBootTest
@ActiveProfiles("test")
public class MidjourneyServiceTest {

    @Resource
    private MidjourneyService midjourneyService;

    @Resource
    private MidjourneyConfig midjourneyConfig;

    private User testUser;

    @BeforeEach
    void setUp() {
        testUser = new User();
        testUser.setId(1L);
        testUser.setLoginName("<EMAIL>");
    }

    /**
     * 测试图像生成
     * 注意：这个测试需要有效的API密钥才能运行
     */
    @Test
    void testImagine() {
        // 跳过测试如果没有配置API密钥
        if ("YOUR_TT_API_KEY_HERE".equals(midjourneyConfig.getApiKey())) {
            System.out.println("跳过测试：请先配置有效的API密钥");
            return;
        }

        try {
            String prompt = "a cute cat sitting on a chair";
            String mode = "fast";

            MidjourneyResponse.BaseResponseData result = midjourneyService.imagine(prompt, mode, testUser);

            System.out.println("生成任务ID: " + result.getJobId());
            assert result.getJobId() != null;

        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试任务状态查询
     */
    @Test
    void testGetTaskStatus() {
        // 跳过测试如果没有配置API密钥
        if ("YOUR_TT_API_KEY_HERE".equals(midjourneyConfig.getApiKey())) {
            System.out.println("跳过测试：请先配置有效的API密钥");
            return;
        }

        try {
            // 使用一个示例jobId（实际测试时需要使用真实的jobId）
            String jobId = "test-job-id";

            MidjourneyResponse.TaskStatusResponse result = midjourneyService.getTaskStatus(jobId);

            System.out.println("任务状态: " + result.getStatus());

        } catch (Exception e) {
            System.err.println("查询状态失败: " + e.getMessage());
            // 这是预期的，因为jobId不存在
        }
    }

    /**
     * 测试图像合成
     */
    @Test
    void testBlend() {
        // 跳过测试如果没有配置API密钥
        if ("YOUR_TT_API_KEY_HERE".equals(midjourneyConfig.getApiKey())) {
            System.out.println("跳过测试：请先配置有效的API密钥");
            return;
        }

        try {
            // 示例Base64图像数据（实际测试时需要使用真实的图像数据）
            List<String> imageBase64List = Arrays.asList(
                "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==",
                "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="
            );

            String dimensions = "SQUARE";
            String mode = "fast";

            MidjourneyResponse.BaseResponseData result = midjourneyService.blend(imageBase64List, dimensions, mode, testUser);

            System.out.println("合成任务ID: " + result.getJobId());
            assert result.getJobId() != null;

        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试图像描述
     */
    @Test
    void testDescribe() {
        // 跳过测试如果没有配置API密钥
        if ("YOUR_TT_API_KEY_HERE".equals(midjourneyConfig.getApiKey())) {
            System.out.println("跳过测试：请先配置有效的API密钥");
            return;
        }

        try {
            // 示例Base64图像数据
            String imageBase64 = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==";
            String mode = "fast";

            MidjourneyResponse.BaseResponseData result = midjourneyService.describe(imageBase64, null, mode, testUser);

            System.out.println("描述任务ID: " + result.getJobId());
            assert result.getJobId() != null;

        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试操作执行
     */
    @Test
    void testAction() {
        // 跳过测试如果没有配置API密钥
        if ("YOUR_TT_API_KEY_HERE".equals(midjourneyConfig.getApiKey())) {
            System.out.println("跳过测试：请先配置有效的API密钥");
            return;
        }

        try {
            // 使用一个示例jobId（实际测试时需要使用真实的jobId）
            String jobId = "test-job-id";
            String action = "upsample1";

            MidjourneyResponse.BaseResponseData result = midjourneyService.action(jobId, action, testUser);

            System.out.println("操作任务ID: " + result.getJobId());

        } catch (Exception e) {
            System.err.println("操作失败: " + e.getMessage());
            // 这是预期的，因为jobId不存在
        }
    }

    /**
     * 测试Prompt检查
     */
    @Test
    void testPromptCheck() {
        // 跳过测试如果没有配置API密钥
        if ("YOUR_TT_API_KEY_HERE".equals(midjourneyConfig.getApiKey())) {
            System.out.println("跳过测试：请先配置有效的API密钥");
            return;
        }

        try {
            // 测试正常的prompt
            String normalPrompt = "a beautiful landscape";
            MidjourneyResponse.PromptCheckResponse result1 = midjourneyService.promptCheck(normalPrompt);

            System.out.println("正常Prompt检查结果:");
            System.out.println("Status: " + result1.getStatus());
            System.out.println("Message: " + result1.getMessage());
            System.out.println("Data: " + result1.getData());

            // 测试可能包含敏感词的prompt
            String sensitivePrompt = "a sexy girl";
            MidjourneyResponse.PromptCheckResponse result2 = midjourneyService.promptCheck(sensitivePrompt);

            System.out.println("\n敏感Prompt检查结果:");
            System.out.println("Status: " + result2.getStatus());
            System.out.println("Message: " + result2.getMessage());
            System.out.println("Data: " + result2.getData());

        } catch (Exception e) {
            System.err.println("Prompt检查失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
