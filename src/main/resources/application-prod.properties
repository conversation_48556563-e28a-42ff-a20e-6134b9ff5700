# --------------------------------------------------数据源配置-----------------------------------------------------------------
spring.shardingsphere.datasource.names=ds0
spring.shardingsphere.datasource.ds0.type=com.zaxxer.hikari.HikariDataSource
spring.shardingsphere.datasource.ds0.driver-class-name=com.mysql.cj.jdbc.Driver
spring.shardingsphere.datasource.ds0.jdbc-url=*******************************************************************************************************************************************************************************************************
spring.shardingsphere.datasource.ds0.username=ne
spring.shardingsphere.datasource.ds0.password=HiwjqefiwojfsdfG88#
# 最小空闲连接数：数据库连接池保持的最小空闲连接数量，当池中的空闲连接少于该值时，HikariCP 会尝试创建新的连接。
spring.shardingsphere.datasource.ds0.hikari.minimum-idle=10
# 最大连接数：连接池中允许的最大连接数，防止数据库连接过多而导致资源耗尽。
spring.shardingsphere.datasource.ds0.hikari.maximum-pool-size=40
# 空闲连接超时时间：空闲连接在池中保留的时间（以毫秒为单位），超过这个时间未被使用的连接将被关闭。
spring.shardingsphere.datasource.ds0.hikari.idle-timeout=300000  # 5 分钟
# 连接超时时间：获取数据库连接时的最大等待时间，超过这个时间未获取到连接将抛出异常。
spring.shardingsphere.datasource.ds0.hikari.connection-timeout=30000  # 30 秒
# 最大存活时间：一个连接在池中最长存活时间，超出这个时间的连接将被标记为过期，HikariCP 将自动重建。
spring.shardingsphere.datasource.ds0.hikari.max-lifetime=900000  # 15 分钟
# 连接池名称：为连接池命名，便于日志记录和调试。
spring.shardingsphere.datasource.ds0.hikari.pool-name=HikariPool-1
# 验证超时时间：用于验证连接有效性的操作超时时间，超过该时间将抛出异常。
spring.shardingsphere.datasource.ds0.hikari.validation-timeout=60000  # 1 分钟
# 泄漏检测阈值：连接超过此时间未关闭时，HikariCP 会记录泄漏日志，用于检测连接泄漏问题。
spring.shardingsphere.datasource.ds0.hikari.leak-detection-threshold=60000  # 1 分钟
# 连接测试查询：数据库连接的有效性测试查询语句，当池中的连接空闲过长时间时将执行此查询以确保连接仍然有效。
spring.shardingsphere.datasource.ds0.hikari.connection-test-query=SELECT 1 FROM DUAL
# 初始化失败超时时间：配置连接池在初始化失败时是否继续重试，设置为 1 表示如果初始化失败会立即抛出异常，而不是无限重试。
spring.shardingsphere.datasource.ds0.hikari.initialization-fail-timeout=1
# 是否在空闲时检测连接：表示在连接空闲时是否通过测试查询验证其有效性，这有助于清除失效的连接。
spring.shardingsphere.datasource.ds0.hikari.test-while-idle=true
# 是否在借用连接时检测连接：设置为 `false`，表示不在借用时进行连接验证，减少验证操作。
spring.shardingsphere.datasource.ds0.hikari.test-on-borrow=false
# 是否在归还连接时检测连接：设置为 `false`，表示不在归还连接时验证其有效性，减少不必要的开销。
spring.shardingsphere.datasource.ds0.hikari.test-on-return=false
# --------------------------------------------------分库分表规则配置-----------------------------------------------------------------
#逻辑表配置（文件表 gpt-prompt-file）
spring.shardingsphere.rules.sharding.tables.gpt_prompt_file.actual-data-nodes=ds0.gpt_prompt_file_$->{0..19}
#分片键
spring.shardingsphere.rules.sharding.tables.gpt_prompt_file.table-strategy.standard.sharding-column=login_name
#分片算法
spring.shardingsphere.rules.sharding.tables.gpt_prompt_file.table-strategy.standard.sharding-algorithm-name=gpt-prompt-file-hash-mod
#自定义id
spring.shardingsphere.rules.sharding.tables.gpt_prompt_file.key-generate-strategy.column=id
#主键生成策略，使用雪花算法
spring.shardingsphere.rules.sharding.tables.gpt_prompt_file.key-generate-strategy.key-generator-name=snowflake
# 分表算法配置
spring.shardingsphere.rules.sharding.sharding-algorithms.gpt-prompt-file-hash-mod.type=HASH_MOD
# 基于 login_name 字段进行 HASH_MOD 分片，将数据均匀分配到 20 个表中
spring.shardingsphere.rules.sharding.sharding-algorithms.gpt-prompt-file-hash-mod.props.sharding-count=20
#逻辑表配置（任务表 gpt_prompt_record）
spring.shardingsphere.rules.sharding.tables.gpt_prompt_record.actual-data-nodes=ds0.gpt_prompt_record_$->{0..19}
#分片键
spring.shardingsphere.rules.sharding.tables.gpt_prompt_record.table-strategy.standard.sharding-column=login_name
#分片算法
spring.shardingsphere.rules.sharding.tables.gpt_prompt_record.table-strategy.standard.sharding-algorithm-name=gpt-prompt-record-hash-mod
#自定义id
spring.shardingsphere.rules.sharding.tables.gpt_prompt_record.key-generate-strategy.column=id
#主键生成策略，使用雪花算法
spring.shardingsphere.rules.sharding.tables.gpt_prompt_record.key-generate-strategy.key-generator-name=snowflake
# 分表算法配置
spring.shardingsphere.rules.sharding.sharding-algorithms.gpt-prompt-record-hash-mod.type=HASH_MOD
# 基于 login_name 字段进行 HASH_MOD 分片，将数据均匀分配到 10 个表中
spring.shardingsphere.rules.sharding.sharding-algorithms.gpt-prompt-record-hash-mod.props.sharding-count=20
#逻辑表配置（日志表 gpt_event_log）
spring.shardingsphere.rules.sharding.tables.gpt_event_log.actual-data-nodes=ds0.gpt_event_log_$->{2024..2025}_$->{1..12}
# 分片键（sharding-column）
spring.shardingsphere.rules.sharding.tables.gpt_event_log.table-strategy.standard.sharding-column=create_time
# 分片算法类型（standard，带有分片算法的类名）
spring.shardingsphere.rules.sharding.tables.gpt_event_log.table-strategy.standard.sharding-algorithm-name=time-sharding-algorithm
#自定义id
spring.shardingsphere.rules.sharding.tables.gpt_event_log.key-generate-strategy.column=id
#主键生成策略，使用雪花算法
spring.shardingsphere.rules.sharding.tables.gpt_event_log.key-generate-strategy.key-generator-name=snowflake
# 定义自定义的分片算法
spring.shardingsphere.rules.sharding.sharding-algorithms.time-sharding-algorithm.type=TIME_SHARDING
# 分片算法表达式
spring.shardingsphere.rules.sharding.sharding-algorithms.time-sharding-algorithm.props.strategy=standard
spring.shardingsphere.rules.sharding.sharding-algorithms.time-sharding-algorithm.props.algorithmClassName=com.lx.pl.component.TimeShardingAlgorithm
#逻辑表配置（收藏表 gpt_user_collect）
spring.shardingsphere.rules.sharding.tables.gpt_user_collect.actual-data-nodes=ds0.gpt_user_collect_$->{0..19}
#分片键
spring.shardingsphere.rules.sharding.tables.gpt_user_collect.table-strategy.standard.sharding-column=login_name
#分片算法
spring.shardingsphere.rules.sharding.tables.gpt_user_collect.table-strategy.standard.sharding-algorithm-name=gpt-user-collect-hash-mod
#自定义id
spring.shardingsphere.rules.sharding.tables.gpt_user_collect.key-generate-strategy.column=id
#主键生成策略，使用雪花算法
spring.shardingsphere.rules.sharding.tables.gpt_user_collect.key-generate-strategy.key-generator-name=snowflake
# 分表算法配置
spring.shardingsphere.rules.sharding.sharding-algorithms.gpt-user-collect-hash-mod.type=HASH_MOD
# 基于 login_name 字段进行 HASH_MOD 分片，将数据均匀分配到 20 个表中
spring.shardingsphere.rules.sharding.sharding-algorithms.gpt-user-collect-hash-mod.props.sharding-count=20
#
#
#逻辑表配置（收藏文件表 gpt_user_collect_classify）
spring.shardingsphere.rules.sharding.tables.gpt_user_collect_classify.actual-data-nodes=ds0.gpt_user_collect_classify_$->{0..19}
#分片键
spring.shardingsphere.rules.sharding.tables.gpt_user_collect_classify.table-strategy.standard.sharding-column=login_name
#分片算法
spring.shardingsphere.rules.sharding.tables.gpt_user_collect_classify.table-strategy.standard.sharding-algorithm-name=gpt-user-collect-classify-hash-mod
#自定义id
spring.shardingsphere.rules.sharding.tables.gpt_user_collect_classify.key-generate-strategy.column=id
#主键生成策略，使用雪花算法
spring.shardingsphere.rules.sharding.tables.gpt_user_collect_classify.key-generate-strategy.key-generator-name=snowflake
# 分表算法配置
spring.shardingsphere.rules.sharding.sharding-algorithms.gpt-user-collect-classify-hash-mod.type=HASH_MOD
# 基于 login_name 字段进行 HASH_MOD 分片，将数据均匀分配到 20 个表中
spring.shardingsphere.rules.sharding.sharding-algorithms.gpt-user-collect-classify-hash-mod.props.sharding-count=20
#绑定表避免跨表查询
spring.shardingsphere.rules.sharding.binding-tables[0]=gpt_prompt_file,gpt_prompt_record
spring.shardingsphere.rules.sharding.binding-tables[1]=gpt_user_collect,gpt_user_collect_classify
# 分布式主键生成器
spring.shardingsphere.default-key-generator=snowflake
spring.shardingsphere.props.worker-id=123
spring.shardingsphere.props.max-tolerate-time-difference-milliseconds=10
spring.shardingsphere.props.max-vibration-offset=1
# SQL 展示
spring.shardingsphere.props.sql-show=true
# 模式配置
spring.shardingsphere.mode.type=Memory
# --------------------------------------------------Redis配置-----------------------------------------------------------------
spring.redis.host=**********
spring.redis.password=
spring.redis.port=6379
spring.redis.lettuce.pool.max-idle=10
spring.redis.lettuce.pool.max-active=20
spring.redis.lettuce.pool.min-idle=5
spring.redis.lettuce.pool.max-wait=10000
spring.redis.timeout=5000
# ----------------------------------------------------MongoDB-----------------------------------------------------------------
spring.data.mongodb.uri=**************************************************************************************************
# 连接池的最小连接数
spring.data.mongodb.min-pool-size=10
# 连接池的最大连接数
spring.data.mongodb.max-pool-size=100
# 连接超时时间（毫秒），在超过这个时间后会抛出连接超时异常
spring.data.mongodb.connect-timeout=10000
# 等待可用连接的最大等待时间（毫秒），超出这个时间将抛出连接超时异常
spring.data.mongodb.max-wait-time=60000
# 服务器连接的心跳检查间隔时间（毫秒）
spring.data.mongodb.heartbeat-frequency=10000
# 最大闲置连接数
spring.data.mongodb.max-idle-time=60000
# 写入操作的超时时间（毫秒）
spring.data.mongodb.write-concern.timeout=5000
# 读取操作的超时时间（毫秒）
spring.data.mongodb.socket-timeout=60000
# 读取偏好，常见值有 primary（主节点） 和 secondaryPreferred（优先从从节点读取）
spring.data.mongodb.read-preference=primaryPreferred
# 写关注级别，"acknowledged" 表示数据被确认写入
spring.data.mongodb.write-concern.acknowledged=true
# 启用或禁用日志记录，以便排查潜在问题
logging.level.org.springframework.data.mongodb.core.MongoTemplate=INFO
# 配置自动索引创建，适合开发和测试环境，生产环境中建议手动管理索引
spring.data.mongodb.auto-index-creation=false
# ----------------------------------------------------系统配置-----------------------------------------------------------------
#Swagger
springdoc.swagger-ui.enabled=true
springdoc.swagger-ui.operations-sorter=alpha
#ComfyUI
comfyui.host=http://ycores.x3322.net:37860
comfyui.websocket.host=ws://ycores.x3322.net:37860
image.storage.path.prefix=/workspace/xiang/piclumen_images/
image.download.prefix=https://s1.piclumen.com/gen-images/
#Nudity detect
nd.url=http://***********:8003/detect_nudity
#Log Level
logging.level.com.lx.pl.db.mysql=debug
logging.level.com.lx.pl.service=debug
#backend(py后端)
apikey=acfc0073-155a-4d30-aab6-8b718f8f5108
callback_url=http://***********:48080/api/gen/call-back
callback_apikey=db73f5b9-7d99-43c6-b07c-96ebe36916e1
backend.baseUrl=http://***********:8002/
translation.url=http://***********:5000/
img2text.url=http://***********:6000/
languageTranslate.url=http://***********:5000/
ImgControl.url=http://***********:8099
img2Ghibli.url=http://*************:8190
#腾讯云设置
#tencent.appId=piclumen-1324066212
#tencent.secretId=AKIDb9XjhRyaVMsBHSbaM1Yt593i9duxnUHd
#tencent.secretKey=IWxEa8X2TjsvnSKEpJbhZDejmeSL6e9V
#tencent.region=na-siliconvalley
#tencent.bucket=piclumen-1324066212
#tencent.cosSuffix=cos.accelerate.myqcloud.com
tencent.bucket=prod-piclumen-1324066212
tencent.cos.domain=https://img.piclumen.com
tencent.cos-accelerate.domain=https://images.piclumen.com
tencent.base.suffix=cos.na-siliconvalley.myqcloud.com
tencent.accelerate.cosSuffix=cos.accelerate.myqcloud.com
tencent.internal-accelerate.cosSuffix=cos-internal.accelerate.tencentcos.cn
#图片文件夹设置
avatar.folder=/prodAvatarPicture/
thumbnail.avatar.folder=/prodThumbnailAvatarPicture/
ai.create.folder=/prodAiPicture/
thumbnail.ai.create.folder=/prodThumbnailPicture/
high.thumbnail.ai.create.folder=/prodHighThumbnailPicture/
album.folder=/prodAlbumPic/
albumThumb.folder=/prodAlbumThumbPic/
localRedraw.folder=/prodLocalRedrawPicture/
#home页运维账户
opex.loginName=<EMAIL>,\
<EMAIL>,\
<EMAIL>,\
<EMAIL>,\
<EMAIL>,\
<EMAIL>,\
<EMAIL>,\
<EMAIL>,\
<EMAIL>
#开启badWord过滤（支持安卓过审）
bad.words.filter=false
#开启kpi统计定时任务，发送邮件
statistics.kpi=true
#上传用户相册数量限制
logic.album.maxImgNums=30
#用户生图任务阈值
user.maxTaskNums=50
#服务器平均排队数阈值（超过则代表服务器繁忙）
server.busyNums=10
#快速生图时间（单位：秒）
fast.hours=1800
#apple登录配置
apple.auth.url=https://appleid.apple.com/auth/keys
apple.iss.url=https://appleid.apple.com
### 执行器通讯TOKEN [选填]：非空时启用；
xxl.job.accessToken=default_token
### 调度中心部署根地址 [选填]：如调度中心集群部署存在多个地址则用逗号分隔。执行器将会使用该地址进行"执行器心跳注册"和"任务结果回调"；为空则关闭自动注册；
xxl.job.admin.addresses=http://**********:48885/xxl-job-admin,http://***********:48885/xxl-job-admin
### 执行器AppName [选填]：执行器心跳注册分组依据；为空则关闭自动注册
xxl.job.executor.appname=piclumen
### 执行器注册 [选填]：优先使用该配置作为注册地址，为空时使用内嵌服务 ”IP:PORT“ 作为注册地址。从而更灵活的支持容器类型执行器动态IP和动态映射端口问题。
xxl.job.executor.address=
### 执行器IP [选填]：默认为空表示自动获取IP，多网卡时可手动设置指定IP，该IP不会绑定Host仅作为通讯实用；地址信息用于 "执行器注册" 和 "调度中心请求并触发任务"；致
xxl.job.executor.ip=
### 执行器端口号 [选填]：小于等于0则自动获取；默认端口为9999，单机部署多个执行器时，注意要配置不同执行器端口；
xxl.job.executor.port=9999
### 执行器运行日志文件存储磁盘路径 [选填] ：需要对该路径拥有读写权限；为空则使用默认路径；
xxl.job.executor.logpath=/home/<USER>/piclumen/job-handler
### 执行器日志文件保存天数 [选填] ： 过期日志自动清理, 限制值大于等于3时生效; 否则, 如-1, 关闭自动清理功能；
xxl.job.executor.logretentiondays=3
## MQ
rocketmq.producer.endpoints=rmq-58xzpeoqr.rocketmq.hk.public.tencenttdmq.com:8080
rocketmq.producer.accessKey=ak58xzpeoqre08c9cfe6b9a
rocketmq.producer.secretKey=sk7ea7d0db968550a3
rocketmq.pushConsumer.endpoints=rmq-58xzpeoqr.rocketmq.hk.public.tencenttdmq.com:8080
rocketmq.pushConsumer.access-key=ak58xzpeoqre08c9cfe6b9a
rocketmq.pushConsumer.secret-key=sk7ea7d0db968550a3
rocketmq.piclumen.topic=tp_piclumen_prod
rocketmq.piclumen.create.group=gid_piclumen_create_prod
rocketmq.piclumen.create.tag=tag_piclumen_create_prod
## ready queue
rocketmq.piclumen.ready.group=gid_piclumen_ready_prod
rocketmq.piclumen.ready.tag=tag_piclumen_ready_prod
## flush score queue
rocketmq.piclumen.flush.group=gid_piclumen_flush_prod
rocketmq.piclumen.flush.topic=tp_piclumen_flush_prod
rocketmq.piclumen.flush.tag=tag_piclumen_flush_prod
## rmgb
rocketmq.piclumen.rmbg.topic=tp_rmbg_prod
rocketmq.piclumen.rmbg.group=gid_rmbg-ok_prod
rocketmq.piclumen.rmbg-ok.tag=tag_rmbg-ok_prod
rocketmq.piclumen.rmbg.tag=tag_rmbg_prod
# ----------------------------------------------------------stripe config ---------------------------------------------------------------------
stripe.client.secretKey=***********************************************************************************************************
stripe.client.pubKey=pk_live_51Q8zbuJkomjQvWKvjGiVnT55OXkg83tN25CzaRtpJqdfhayqWgmLWxaifiic9U2MDvFM0M5xlxXZEO140ETBaLhp00g8SGuqYf
stripe.client.webhookSecret_customer=whsec_UpBgBjZ3UhfaBuK2431CQIRo7SjqXqZ7
# -----------------------------------------------------------Apple--------------------------------------------------------------------
pay.apple.env=Production
knife4j.enable=false
knife4j.production=true
knife4j.basic.enable=true
#google登录配置
google.oauth.login.client-id=765522450246-gt8mqu2k3o2r2adm5nj269smgomeg12n.apps.googleusercontent.com
google.oauth.login.client-secret=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
#--------------------------------------------------------------PUSH----------------------------------------------------------------------------
push.notification-config.apnsP8TeamId=Z3H8NYT2V9
push.notification-config.apnsP8KeyId=WN83374QLC
push.notification-config.apnsP8FilePath=notification/AuthKey_WN83374QLC.p8
## 测试环境地址 api.development.push.apple.com 正式环境地址 api.push.apple.com
push.notification-config.apnsEnvironment=api.development.push.apple.com
push.notification-config.apnsAppBundledId=com.piclumen
push.notification-config.fcmMessagingScope=https://www.googleapis.com/auth/firebase.messaging
## 注意  projects/这个地方是 projectId 需要从控制台获取 /message
push.notification-config.fcmIOSEndPoint=https://fcm.googleapis.com/v1/projects/piclumen-c034b/messages:send
push.notification-config.fcmIOSFilePath=notification/piclumen-c034b-firebase-adminsdk-m9az9-6dbe248524.json
## 注意  projects/这个地方是 projectId 需要从控制台获取 /message
#push.notification-config.fcmAndroidEndPoint=
#push.notification-config.fcmAndroidFilePath=
# ----------------------------------------------------------Midjourney API config ---------------------------------------------------------------------
# Midjourney API配置
midjourney.api.base-url=https://api.ttapi.io
midjourney.api.api-key=73cca588-d4f8-b59f-a188-7bbffef5b595
midjourney.api.callback-url=https://piclumen.com/api/midjourney/callback/webhook
midjourney.api.default-timeout=300
midjourney.api.max-retries=3
midjourney.api.translation-enabled=true
midjourney.api.default-mode=fast
midjourney.api.prompt-check-enabled=true