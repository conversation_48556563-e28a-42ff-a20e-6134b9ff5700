package com.lx.pl.job;


import com.lx.pl.constant.LogicConstants;
import com.lx.pl.lb.DealTaskScheduler;
import com.lx.pl.service.*;
import com.lx.pl.service.message.PushNotifyService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

@Slf4j
@Component
public class XxlJobTask {

    private static final Logger logger = LoggerFactory.getLogger("schedule-task");

    @Value("${statistics.kpi}")
    Boolean statisticsKpi;

    @Autowired
    StatisticsService statisticsService;

    @Autowired
    ResettingUserMessageService messageService;

    @Autowired
    LoadBalanceService loadBalanceService;

    @Autowired
    EventLogService eventLogService;

    @Autowired
    DealTaskScheduler dealTaskScheduler;
    @Autowired
    private FileScoreService fileScoreService;
    @Autowired
    private PushNotifyService pushNotifyService;
    @Autowired
    private RedissonClient redissonClient;

    @XxlJob("resettingImgNumTask")
    public ReturnT resettingImgNum() {
        logger.info("重置用户当日生图数量任务");
        try {
            messageService.resettingUserCreateImgNumTask();
            messageService.resettingUserPublicImgNum();
            messageService.resettingUserLumens();
        } catch (Exception e) {
            logger.error("重置用户当日生图数量报错", e);
        }
        return ReturnT.SUCCESS;
    }

    @XxlJob("dealProcessTask")
    public ReturnT dealProcess() {
        try {
            loadBalanceService.senQueueIndex();
        } catch (Exception e) {
            logger.error("任务队列发送排队信息报错", e);
        }
        return ReturnT.SUCCESS;
    }

    @XxlJob("loadBalanceConsumerTask")
    public ReturnT loadBalanceConsumer() {
        try {
            dealTaskScheduler.processTasks2();
        } catch (Exception e) {
            logger.error("消费任务队列报错", e);
        }
        return ReturnT.SUCCESS;
    }


    @XxlJob("dealRelaxWaitQueueTask")
    public ReturnT dealRelaxWaitQueue() {
        try {
            loadBalanceService.sendRelaxWaitQueue();
        } catch (Exception e) {
            logger.error("relax等待队列发送非公平排队队列报错", e);
        }
        return ReturnT.SUCCESS;
    }

    @XxlJob("flushFileScore")
    public ReturnT flushFileScore() {
        try {
            fileScoreService.updateFileScore();
        } catch (Exception e) {
            logger.error("flushFileScore报错", e);
        }
        return ReturnT.SUCCESS;
    }

    @XxlJob("initFlushData")
    public ReturnT initFlushData() {
        try {
            fileScoreService.initData();
        } catch (Exception e) {
            logger.error("initadata error", e);
        }
        return ReturnT.SUCCESS;
    }

    //PUSH定时任务
    @XxlJob("pushNotificationExecuteTask")
    public void pushNotificationExecuteTask() {
        RLock lock = redissonClient.getLock(LogicConstants.XXL_JOB_TASK_PUSH_LOCK_KEY);
        try {
            if (!lock.tryLock()) {
                log.info("上次PUSH任务还没执行完, 跳过此次任务");
                return;
            }
            pushNotifyService.getAndExecute(0, LocalDateTime.now());
        } catch (Exception e) {
            log.error("PUSH任务执行异常, ", e);
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }
}
