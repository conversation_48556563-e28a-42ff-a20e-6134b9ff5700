package com.lx.pl.pay.stripe.service.strategy.customer;

import cn.hutool.core.util.StrUtil;
import com.lx.pl.db.mysql.gen.entity.StripeProduct;
import com.lx.pl.pay.apple.VipPlatform;
import com.lx.pl.pay.common.domain.SubscriptionCurrent;
import com.lx.pl.pay.common.service.impl.SubscriptionCurrentServiceImpl;
import com.lx.pl.pay.stripe.annotation.StripeEvent;
import com.lx.pl.pay.stripe.domain.StripeSubscriptionRecord;
import com.lx.pl.pay.stripe.service.PayLogicPurchaseRecordService;
import com.lx.pl.pay.stripe.service.StripeSubscriptionLogService;
import com.lx.pl.pay.stripe.service.StripeSubscriptionRecordService;
import com.lx.pl.pay.stripe.service.StripeTrialLogService;
import com.lx.pl.pay.stripe.service.strategy.IStripeEventHandler;
import com.lx.pl.service.StripeProductService;
import com.lx.pl.service.UserService;
import com.stripe.model.Discount;
import com.stripe.model.Subscription;
import org.redisson.api.RLock;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;

import static com.lx.pl.pay.PayConstant.SUBSCRIPTION_LOCK_PREFIX;

/**
 * Occurs whenever a customer is signed up for a new plan.
 *
 * <AUTHOR>
 */
@Component
@StripeEvent(eventType = "customer.subscription.created")
public class CustomerSubscriptionCreateEvent extends IStripeEventHandler<Subscription> {

    @Resource
    private StripeSubscriptionRecordService stripeSubscriptionRecordService;
    @Resource
    private StripeSubscriptionLogService stripeSubscriptionLogService;
    @Resource
    private PayLogicPurchaseRecordService payLogicPurchaseRecordService;
    @Resource
    private UserService userService;
    @Resource
    private StripeProductService stripeProductService;
    @Resource
    private StripeTrialLogService trialLogService;

    @Override
    public void handleEvent(Subscription event, String eventId) {
        String id = event.getId();
        RLock lock = redissonClient.getLock(SUBSCRIPTION_LOCK_PREFIX + id);
        log.info("lock customer.subscription.created: {} {} {}", event.getCustomer(), eventId, SUBSCRIPTION_LOCK_PREFIX + id);
        try {
            lock.lock();
            String loginName = applicationContext.getBean(CustomerSubscriptionCreateEvent.class)
                    .doHandleLogic(event, eventId);
            if (StrUtil.isNotBlank(loginName)) {
                vipService.resettingPersonalLumens(loginName);
            }
        } finally {
            if (lock.isHeldByCurrentThread() && lock.isLocked()) {
                lock.unlock();
                log.info("unlock customer.subscription.created: {} {} {}", event.getCustomer(), eventId, SUBSCRIPTION_LOCK_PREFIX + id);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public String doHandleLogic(Subscription event, String eventId) {
        log.info("start customer.subscription.created: {} {} {}", event.getCustomer(), event.getId(), event);
        stripeSubscriptionLogService.saveSubscriptionLog(event);
        StripeSubscriptionRecord one = stripeSubscriptionRecordService.lambdaQuery()
                .eq(StripeSubscriptionRecord::getSubscriptionId, event.getId()).one();
        if (one == null) {
            one = stripeSubscriptionRecordService.saveSubscription(event);
            // 订单完成的处理逻辑
            log.info("save customer.subscription.created: {} {}", event.getCustomer(), event.getId());
        }
        StripeProduct stripeProduct = stripeProductService.lambdaQuery()
                .eq(StripeProduct::getStripePriceId, one.getPriceId()).one();
        if (stripeProduct == null) {
            log.error("StripeProduct not found {} {}", event.getCustomer(), event.getId());
            throw new RuntimeException("StripeProduct not found");
        }
        if (one != null && "active".equals(one.getSubStatus())) {
            // 计算并保存逻辑发放记录
            boolean isSave = payLogicPurchaseRecordService.saveLogicPurchaseRecord(one, event.getLatestInvoice(), stripeProduct, false);
            log.info("{} save customer.subscription.created: {} customer: {}", isSave, event.getCustomer(), event.getId());
            if (isSave) {

                subscriptionCurrentService.saveOrUpdateStripeSubscriptionCurrent(buildSubscriptionCurrent(one, stripeProduct, false, event.getDiscount()));
            }
            SubscriptionCurrent validHighSubscriptionsFromDb = subscriptionCurrentService.getLogicValidHighSubscriptionsFromDb(one.getUserId());
            userService.updateUserVipInfo(validHighSubscriptionsFromDb, one.getUserId());
            return one.getLoginName();
        } else if (one != null && "trialing".equals(one.getSubStatus())) {
            log.info("start trialing customer.subscription.created: {} {} {}", event.getCustomer(), event.getTrialStart(), event.getTrialEnd());
            trialLogService.saveTrailLog(one, stripeProduct);
            boolean isSave = payLogicPurchaseRecordService.saveLogicPurchaseRecord(one, event.getLatestInvoice(), stripeProduct, true);
            log.info("{} save  trialing customer.subscription.created: {} customer: {}", isSave, event.getCustomer(), event.getId());
            if (isSave) {
                subscriptionCurrentService.saveOrUpdateStripeSubscriptionCurrent(buildSubscriptionCurrent(one, stripeProduct, true, event.getDiscount()));
            }
            SubscriptionCurrent validHighSubscriptionsFromDb = subscriptionCurrentService.getLogicValidHighSubscriptionsFromDb(one.getUserId());
            userService.updateUserVipInfo(validHighSubscriptionsFromDb, one.getUserId());
            return one.getLoginName();
        }
        log.info("end customer.subscription.created: {} {}", event.getCustomer(), event.getId());
        return null;
    }

    protected static SubscriptionCurrent buildSubscriptionCurrent(StripeSubscriptionRecord one, StripeProduct product, boolean isTrial, Discount discount) {
        SubscriptionCurrent subscriptionCurrent = new SubscriptionCurrent();
        subscriptionCurrent.setUserId(one.getUserId());
        subscriptionCurrent.setLoginName(one.getLoginName());
        subscriptionCurrent.setSubscriptionId(one.getSubscriptionId());
        subscriptionCurrent.setVipPlatform(VipPlatform.STRIPE.getPlatformName());
        subscriptionCurrent.setPlanLevel(product.getPlanLevel());
        subscriptionCurrent.setCurrentPeriodStart(one.getCurrentPeriodStart());
        subscriptionCurrent.setCurrentPeriodEnd(one.getCurrentPeriodEnd());
        subscriptionCurrent.setPriceInterval(product.getPriceInterval());
        subscriptionCurrent.setTrial(isTrial);
        if (product.getMark() == null) {
            subscriptionCurrent.setMark("v1");
        } else {
            subscriptionCurrent.setMark(product.getMark());
        }
        Long cancelledAt = one.getCancelledAt();
        if (cancelledAt == null) {
            subscriptionCurrent.setAutoRenewStatus(1);
        } else {
            subscriptionCurrent.setAutoRenewStatus(0);
        }
        subscriptionCurrent.setCreateBy("stripe");
        subscriptionCurrent.setCreateTime(LocalDateTime.now());
        subscriptionCurrent.setInvalid(false);
        SubscriptionCurrentServiceImpl.buildRenewPrice(product, discount, subscriptionCurrent);
        return subscriptionCurrent;
    }
}