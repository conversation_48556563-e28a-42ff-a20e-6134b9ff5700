package com.lx.pl.pay.stripe.service.strategy.customer;

import cn.hutool.core.util.StrUtil;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.pay.common.domain.SubscriptionCurrent;
import com.lx.pl.pay.stripe.annotation.StripeEvent;
import com.lx.pl.pay.stripe.domain.StripeSubscriptionRecord;
import com.lx.pl.pay.stripe.service.PayLogicPurchaseRecordService;
import com.lx.pl.pay.stripe.service.StripeSubscriptionLogService;
import com.lx.pl.pay.stripe.service.StripeSubscriptionRecordService;
import com.lx.pl.pay.stripe.service.strategy.IStripeEventHandler;
import com.lx.pl.service.UserService;
import com.lx.pl.service.VipService;
import com.stripe.model.Subscription;
import org.redisson.api.RLock;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;

import static com.lx.pl.pay.PayConstant.SUBSCRIPTION_LOCK_PREFIX;

/**
 * Occurs whenever a subscription changes
 * (e.g., switching from one plan to another, or changing the status from trial to active).
 *
 * <AUTHOR>
 */
@Component
@StripeEvent(eventType = "customer.subscription.deleted")
public class CustomerSubscriptionDeleteEvent extends IStripeEventHandler<Subscription> {

    @Resource
    private StripeSubscriptionRecordService stripeSubscriptionRecordService;
    @Resource
    private PayLogicPurchaseRecordService payLogicPurchaseRecordService;
    @Resource
    private StripeSubscriptionLogService stripeSubscriptionLogService;
    @Resource
    private UserService userService;
    @Resource
    private VipService vipService;

    @Override
    public void handleEvent(Subscription event, String eventId) {
        String id = event.getId();
        RLock lock = redissonClient.getLock(SUBSCRIPTION_LOCK_PREFIX + id);
        log.info("lock customer.subscription.deleted: {} {} {}", event.getCustomer(), eventId, SUBSCRIPTION_LOCK_PREFIX + id);
        String loginName = null;
        try {
            lock.lock();
            loginName = applicationContext.getBean(CustomerSubscriptionDeleteEvent.class).doHandleLogic(event);
            if (StrUtil.isNotBlank(loginName)) {
                vipService.resettingPersonalLumens(loginName);
            }
        } finally {
            if (lock.isHeldByCurrentThread() && lock.isLocked()) {
                lock.unlock();
                log.info("unlock customer.subscription.deleted: {} {} {}", event.getCustomer(), eventId, SUBSCRIPTION_LOCK_PREFIX + id);
            }
            try {
                if (StrUtil.isNotBlank(loginName)) {
                    User user = userService.getByLoginName(loginName);
                    if (user != null) {
                        updateUserVipStatus(user.getId());
                    }
                }
            } catch (Exception e) {
                log.error("更新用户VIP等级2 失败: {}", e.getMessage(), e);
            }
        }
    }

    private void updateUserVipStatus(Long userId) {
        SubscriptionCurrent subscription = subscriptionCurrentService.getLogicValidHighSubscriptionsFromDb(userId);
        log.info("更新用户VIP等级2 start: loginName={} newPlanLevel={} priceInterval={}", subscription.getLoginName(), subscription.getPlanLevel(), subscription.getPriceInterval());
        userService.updateUserVipInfo(subscription, userId);
        log.info("更新用户VIP等级2 end: loginName={}, newPlanLevel={} priceInterval={}", subscription.getLoginName(), subscription.getPlanLevel(), subscription.getPriceInterval());
    }


    @Transactional(rollbackFor = Exception.class)
    public String doHandleLogic(Subscription event) {
        log.info("start customer.subscription.deleted: {} {}", event.getCustomer(), event.getId());
        stripeSubscriptionLogService.saveSubscriptionLog(event);
        StripeSubscriptionRecord subscription = stripeSubscriptionRecordService.lambdaQuery()
                .eq(StripeSubscriptionRecord::getSubscriptionId, event.getId())
                .one();
        if (subscription == null) {
            log.error("can not find subscription record customer.subscription.deleted: {} {}", event.getCustomer(), event.getId());
            throw new RuntimeException("can not find subscription record");
        }
        Subscription.CancellationDetails cancellationDetails = event.getCancellationDetails();
        if (cancellationDetails != null) {
            subscription.setCancelReason(cancellationDetails.getReason());
        }
        subscription.setSubStatus(event.getStatus());
        stripeSubscriptionRecordService.lambdaUpdate()
                .eq(StripeSubscriptionRecord::getSubscriptionId, event.getId())
                .set(StripeSubscriptionRecord::getSubStatus, event.getStatus())
                .set(event.getCanceledAt() != null, StripeSubscriptionRecord::getCancelledAt, event.getCanceledAt())
                .set(StripeSubscriptionRecord::getCancelReason, cancellationDetails != null ? cancellationDetails.getReason() : null)
                .set(StripeSubscriptionRecord::getUpdateTime, LocalDateTime.now())
                .update();
        if ("canceled".equals(subscription.getSubStatus()) && "payment_failed".equals(subscription.getCancelReason())) {
            // 计算并保存逻辑发放记录
            boolean successRecall = payLogicPurchaseRecordService.recallLogicPurchaseRecord(subscription, event.getLatestInvoice(), subscription.getCancelReason());
            if (successRecall) {
                log.info("recall logic purchase record success customer.subscription.deleted: {} {}", subscription.getCustomerId(), subscription.getSubscriptionId());
                subscriptionCurrentService.recallStripeVipTime(subscription);
            }
        }
        log.info("end customer.subscription.deleted: {} {}", event.getCustomer(), event.getId());
        return subscription.getLoginName();
    }
}