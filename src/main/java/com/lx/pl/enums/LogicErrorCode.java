package com.lx.pl.enums;

/**
 * @Description: logic处理异常枚举，描述不返回前端，用于细分错误码，message 描述前端显示错误
 * @Author: senlin_he
 * @Date: 2024/12/27
 */
public enum LogicErrorCode {
    ILLEGAL_PROMPT("4001", "Sorry!  The AI detected that this prompt might break out community rules. Please try a different one.", "提示词涉及儿童色情词汇"),

    EXCEED_CONCURRENT_JOBS("4002", "Exceed  Concurrent Jobs ", "超过并发任务限制"),

    EXCEED_TASK_QUEUE("4003", "Exceed_TaskQueue ", "超过预载队列现在"),
    NOT_VIP("4005", "Please recharge VIP membership first ", "用户非vip"),

    NOT_ENOUGH_LUMENS_SUPPORT("4006", "Please recharge VIP membership and buy lumens first ", "用户不够dev权限"),

    NOT_TASK_EXIST("4007", "Task does not exist ", "任务已经被删除"),

    NOT_MODEL_SUPPORT("4008", "Model required ", "用户没有传modelId"),

    PIXELS_EXCEED_LIMIT("4009", "Pixels exceed limit", "像素超过限制"),

    CONTINUE_CREATE("4011", "Continue Create", "忽略告警，继续生图"),

    RELAX_CREATE_LIMIT("4012", "Relax Create Limit", "非会员，闲时生图达到上线"),

    UNKNOWN_ERROR("4999", "A generic error occurred during logic processing.", "处理逻辑时发生未知异常"),

    USER_PUBLIC_ACTIVITY_LIMIT("4013", "Your image has reached this activity limit.", "用户活动投稿超过次活动限制"),

    USER_ACTIVITY_TIME_PASSED("4014", "The deadline for submitting entries has passed.", "活动投稿时间已过"),

    USER_PUBLIC_RESUBMIT("4015", "Please do not resubmit.", "重复提交"),

    NOT_DELETE_WIN_IMAGE("4016", "This image has won an award and cannot be deleted.", "获奖图片不能删除"),

    BATCH_RMBG_TASK_LIMITED("4017", "A small system hiccup. Please try again later.", "批量去背景任务数已达上限"),

    BATCH_RMBG_BATCH_ID_EMPTY_ERROR("4018", "A small system hiccup. Please try again later.", "批量去背景batchId为空"),

    // Midjourney相关错误码
    MIDJOURNEY_API_ERROR("4019", "Midjourney API call failed", "Midjourney API调用失败"),

    MIDJOURNEY_PROMPT_CHECK_FAILED("4020", "Prompt check failed", "Prompt检查失败"),

    MIDJOURNEY_GENERATION_FAILED("4021", "Image generation failed", "图像生成失败"),

    ILLEGAL_REQUEST("4022", "Illegal request", "非法请求"),

    MODEL_NOT_SUPPORT("4023", "Model not support", "模型不支持"),

    MIDJOURNEY_ACTION_FAILED("4024", "Midjourney action failed", "Midjourney操作失败");


    private final String code;
    private final String message;
    private final String description;

    LogicErrorCode(String code, String message, String description) {
        this.code = code;
        this.message = message;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public String getDescription() {
        return description;
    }
}
