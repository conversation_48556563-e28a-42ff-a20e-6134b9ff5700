package com.lx.pl.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

@Getter
public enum ClientType {

    ios("ios", "苹果App"),
    android("android", "安卓app"),
    web("web", "浏览器端");

    private final String value;

    private final String label;

    //如果枚举值中还有数据则必须要创建一个构造函数
    ClientType(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public static ClientType getByValue(String value) {
        if (StringUtils.isBlank(value)) {
            return web;
        }
        return Arrays.stream(values()).filter(type -> type.value.equalsIgnoreCase(value)).findFirst().orElse(web);
    }

    public static boolean mobilePlatform(String platform) {
        if (StringUtils.isBlank(platform)) {
            return false;
        }
        return ios.getValue().equalsIgnoreCase(platform) || android.getValue().equalsIgnoreCase(platform);
    }
}
