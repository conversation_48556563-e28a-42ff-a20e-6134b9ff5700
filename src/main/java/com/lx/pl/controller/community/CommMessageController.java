package com.lx.pl.controller.community;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.lx.pl.annotation.Authorization;
import com.lx.pl.annotation.CurrentUser;
import com.lx.pl.db.mysql.community.entity.CommComment;
import com.lx.pl.db.mysql.community.entity.CommLike;
import com.lx.pl.db.mysql.community.entity.UserPlatformMessage;
import com.lx.pl.db.mysql.gen.entity.GptPlatformActivity;
import com.lx.pl.db.mysql.gen.entity.GptSysUpdate;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.dto.CommMessageParams;
import com.lx.pl.dto.CommPageInfo;
import com.lx.pl.dto.CommUserMessageNums;
import com.lx.pl.dto.generic.R;
import com.lx.pl.service.CommMessageService;
import com.lx.pl.service.GenService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

@Tag(name = "社区信息通知接口")
@Slf4j
@RestController
@RequestMapping("/api/comm-message")
public class CommMessageController {

    @Autowired
    private CommMessageService commMessageService;

    @Autowired
    GenService genService;

    @Operation(summary = "获取用户未读信息数")
    @GetMapping("/select-message-nums")
    @Authorization
    public R<CommUserMessageNums> getCommUserMessageNums(@CurrentUser @Parameter(hidden = true) User user, HttpServletRequest request) {

        String platform = genService.getPlatform(request);
        if (StringUtil.isBlank(platform)) {
            return R.fail(400, "Illegal request !");
        }
        return R.success(commMessageService.getCommUserMessageNums(user, platform));
    }

    @Operation(summary = "处理已读消息")
    @PostMapping("/deal-read-message")
    @Authorization
    public R<Boolean> reduceCommUserMessageNums(@RequestBody CommMessageParams commMessageParams,
                                                @CurrentUser @Parameter(hidden = true) User user, HttpServletRequest request) {

        String platform = genService.getPlatform(request);
        if (StringUtil.isBlank(platform)) {
            return R.fail(400, "Illegal request !");
        }
        Boolean result = commMessageService.reduceCommUserMessageNums(commMessageParams, user ,platform);
        return result ? R.success(result) : R.fail(1, "Failed");
    }

    @Operation(summary = "处理全部已读消息")
    @PostMapping("/deal-read-all")
    @Authorization
    public R<Boolean> reduceReadAll(@CurrentUser @Parameter(hidden = true) User user, HttpServletRequest request) {

        String platform = genService.getPlatform(request);
        if (StringUtil.isBlank(platform)) {
            return R.fail(400, "Illegal request !");
        }
        Boolean result = commMessageService.reduceReadAll(user, platform);
        return result ? R.success(result) : R.fail(1, "Failed");
    }

    @Operation(summary = "获取点赞已读未读的信息")
    @GetMapping("/select-like-message")
    @Authorization
    public R<CommPageInfo<CommLike>> getLikeMessage(@RequestParam(value = "lastLikeId", required = false) String lastLikeId,
                                                    @RequestParam("pageSize") Integer pageSize,
                                                    @CurrentUser @Parameter(hidden = true) User user) {
        return R.success(commMessageService.getLikeMessage(lastLikeId, pageSize, user));
    }

    @Operation(summary = "获取评论已读未读的信息")
    @GetMapping("/select-comment-message")
    @Authorization
    public R<CommPageInfo<CommComment>> getCommentMessage(@RequestParam(value = "lastCommentId", required = false) String lastCommentId,
                                                          @RequestParam("pageSize") Integer pageSize,
                                                          @CurrentUser @Parameter(hidden = true) User user) {
        return R.success(commMessageService.getCommentMessage(lastCommentId, pageSize, user));
    }

    @Operation(summary = "获取系统更新已读未读的信息")
    @GetMapping("/select-sysUpdate-message")
    @Authorization
    public R<CommPageInfo<GptSysUpdate>> getSysUpdateMessage(@RequestParam(value = "lastSysUpdateId", required = false) String lastSysUpdateId,
                                                             @RequestParam("pageSize") Integer pageSize,
                                                             @CurrentUser @Parameter(hidden = true) User user,
                                                             HttpServletRequest request) {

        String platform = genService.getPlatform(request);
        if (StringUtil.isBlank(platform)) {
            return R.fail(400, "Illegal request !");
        }
        return R.success(commMessageService.getSysUpdateMessage(lastSysUpdateId, pageSize, user, platform));
    }

    @Operation(summary = "获取公告通知已读未读的信息")
    @GetMapping("/select-activity-message")
    @Authorization
    public R<CommPageInfo<GptPlatformActivity>> getActivityMessage(@RequestParam(value = "lastActivityId", required = false) String lastActivityId,
                                                                   @RequestParam("pageSize") Integer pageSize,
                                                                   @CurrentUser @Parameter(hidden = true) User user) {
        return R.success(commMessageService.getActivityMessage(lastActivityId, pageSize, user));
    }

    @Operation(summary = "获取平台消息已读未读的信息")
    @GetMapping("/select-platform-message")
    @Authorization
    public R<CommPageInfo<UserPlatformMessage>> getPlatformMessage(@RequestParam(value = "lastPlatformMessId", required = false) String lastPlatformMessId,
                                                                   @RequestParam("pageSize") Integer pageSize,
                                                                   @CurrentUser @Parameter(hidden = true) User user) {
        return R.success(commMessageService.getPlatformMessage(lastPlatformMessId, pageSize, user));
    }

}
