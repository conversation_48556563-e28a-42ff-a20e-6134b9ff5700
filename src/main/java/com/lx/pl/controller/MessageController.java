package com.lx.pl.controller;

import com.lx.pl.constant.LogicConstants;
import com.lx.pl.enums.ClientType;
import com.lx.pl.enums.PushMessageReportType;
import com.lx.pl.service.RedisService;
import com.lx.pl.service.message.PushNotifyService;
import com.lx.pl.vo.PushMessageReportReq;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/5/12
 * @description
 */
@Tag(name = "PUSH/站内信相关接口")
@Slf4j
@RestController
@RequestMapping("/api/message")
public class MessageController {
    @Resource
    private PushNotifyService pushNotifyService;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private RedisService redisService;

    @Operation(summary = "PUSH打开消息上报")
    @PostMapping("push-open-report")
    public void pushOpenReport(@RequestBody @Valid PushMessageReportReq req, HttpServletRequest request) {
        if (Objects.isNull(PushMessageReportType.getByCode(req.getMessageType()))) {
            log.error("未找到消息上报类型, messageType: {}", req.getMessageType());
            return;
        }
        String platform = request.getHeader("platform");
        if (!ClientType.mobilePlatform(platform)) {
            log.error("非移动端上报不处理, platform: {}", platform);
            return;
        }
        pushNotifyService.pushOpenReport(req);
    }

    @Operation(summary = "触发消息推送")
    @PostMapping("push-task")
    public void pushNotificationExecuteTask() {
        RLock lock = redissonClient.getLock(LogicConstants.XXL_JOB_TASK_PUSH_LOCK_KEY);
        try {
            if (!lock.tryLock()) {
                log.info("上次PUSH任务还没执行完, 跳过此次任务");
                return;
            }
            pushNotifyService.getAndExecute(0, LocalDateTime.now());
        } catch (Exception e) {
            log.error("PUSH任务执行异常, ", e);
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @Operation(summary = "触发自动消息推送")
    @PostMapping("auto-push-task")
    public void dailyPushExecute() {
        log.info("开始执行每日自动任务");
        if (redisService.hasKey(LogicConstants.DISABLE_AUTO_PUSH_KEY)) {
            log.info("自动推送已被禁用");
            return;
        }
        RLock lock = redissonClient.getLock(LogicConstants.DAILY_PUSH_EXECUTE_LOCK_KEY);
        try {
            if (!lock.tryLock()) {
                //其他实例正在执行
                return;
            }
            pushNotifyService.getAndExecute(1, null);
        } catch (Exception e) {
            log.error("执行每日自动PUSH任务异常, ", e);
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }
}
