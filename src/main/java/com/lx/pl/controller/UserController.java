package com.lx.pl.controller;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.lx.pl.annotation.Authorization;
import com.lx.pl.annotation.CurrentUser;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.dto.AppleLoginDTO;
import com.lx.pl.dto.UserDeleteDto;
import com.lx.pl.dto.UserInfo;
import com.lx.pl.dto.UserMessageDetail;
import com.lx.pl.dto.common.Login;
import com.lx.pl.dto.common.LoginOauth;
import com.lx.pl.dto.common.Register;
import com.lx.pl.dto.generic.R;
import com.lx.pl.service.GenService;
import com.lx.pl.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

@Tag(name = "用户相关接口")
@Slf4j
@RestController
@RequestMapping("/api/user")
public class UserController {

    private static final String AUTHORIZATION = "authorization";

    @Autowired
    UserService userService;

    @Autowired
    GenService genService;

    @Operation(summary = "第三方登录，目前支持Google")
    @PostMapping(path = "/login-oauth")
    public R<Login> login(@RequestBody LoginOauth loginOauth, HttpServletRequest request) {
        return R.success(userService.loginOauth(loginOauth, request));
    }

    @Operation(summary = "Google OAuth2.0登录")
    @PostMapping(path = "/login-oauth-with-code")
    public R<Login> loginWithCode(@RequestBody LoginOauth loginOauth, HttpServletRequest request) {
        return R.success(userService.loginOauthWithCode(loginOauth, request));
    }

    @Operation(summary = "登录")
    @PostMapping(path = "/login")
    public R<Login> login(@RequestParam @Parameter(description = "帐号") String account,
                          @RequestParam @Parameter(description = "密码") String password) {
        return R.success(userService.login(account, password));
    }

    @Operation(summary = "退出登录")
    @Authorization
    @PostMapping(path = "/logout")
    public R<String> logout(@Parameter(hidden = true) @CurrentUser User user, HttpServletRequest request) {
        String token = request.getHeader(AUTHORIZATION);
        userService.logout(token, user);
        return R.success();
    }

    @Operation(summary = "查询用户详情")
    @Authorization
    @GetMapping(path = "/info")
    public R<UserInfo> info(@Parameter(hidden = true) @CurrentUser User user, HttpServletRequest request) {
        String platform = genService.getPlatform(request);
        if (StringUtil.isBlank(platform)) {
            return R.fail(400, "Illegal request !");
        }

        return R.success(userService.getUserInfo(user.getId(), platform));
    }

    @Operation(summary = "用户注册")
    @PostMapping(path = "/register")
    public R<String> register(@RequestBody Register register, HttpServletRequest request) {
        userService.register(register, request);
        return R.success();
    }

    @Operation(summary = "用户注册，发送验证码")
    @PostMapping(path = "/register-send-code")
    public R<String> registerSendCode(
            @RequestParam @Parameter(description = "账号，必须是邮箱地址或手机号码") String account, HttpServletRequest request) {
        String platform = genService.getPlatform(request);
        userService.registerSendCode(account, platform);
        return R.success();
    }

    @Operation(summary = "忘记密码，发送验证码")
    @PostMapping(path = "/reset-password-send-code")
    public R<String> resetPasswordSendCode(
            @RequestParam @Parameter(description = "账号，必须是邮箱地址或手机号码") String account, HttpServletRequest request) {
        String platform = genService.getPlatform(request);
        userService.resetPasswordSendCode(account, platform);
        return R.success();
    }

    @Operation(summary = "重置密码")
    @PostMapping(path = "/reset-password")
    public R<String> resetPassword(
            @RequestParam @Parameter(description = "账号，必须是邮箱地址或手机号码") String account,
            @RequestParam @Parameter(description = "验证码") String validateCode,
            @RequestParam @Parameter(description = "新密码") String password) {
        userService.resetPassword(account, validateCode, password);
        return R.success();
    }

//    @Operation(summary = "设置用户个性化配置")
//    @PostMapping(path = "/set-config")
//    @Authorization
//    public R<String> setConfig(
//            @Parameter(description = "本用户的一些配置信息，可以保存2000字") @RequestBody String userConfigItems,
//            @Parameter(hidden = true) @CurrentUser User user) {
//        userService.setConfig(user, userConfigItems);
//        return R.success();
//    }

//    @Operation(summary = "获取用户个性化配置")
//    @GetMapping(path = "/get-config")
//    @Authorization
//    public R<String> getConfig(@Parameter(hidden = true) @CurrentUser User user) {
//        return R.success(userService.getConfig(user));
//    }

    @Operation(summary = "校验用户登录名称是否已经存在")
    @PostMapping(path = "/check-login-name")
    public R<Boolean> checkLoginName(@RequestParam @Parameter(description = "用户登录名称") String loginName) {
        Boolean result = userService.checkLoginName(loginName);
        return result ? R.success(result) : R.fail(1, "Failed");
    }

    @Operation(summary = "校验用户昵称是否已经存在")
    @PostMapping(path = "/check-user-name")
    public R<Boolean> checkUserName(@RequestParam @Parameter(description = "用户昵称") String userName) {
        Boolean result = userService.checkUserName(userName);
        return result ? R.success(result) : R.fail(1, "Failed");
    }

    @Operation(summary = "上传用户头像")
    @PostMapping(path = "/upload-user-avatar")
    @Authorization
    public R<Boolean> uploadUserAvatar(@RequestParam("avatarImg") MultipartFile avatarImg,
                                       @Parameter(hidden = true) @CurrentUser User user) throws IOException {
        Boolean result = userService.uploadUserAvatar(avatarImg, user);
        return result ? R.success(result) : R.fail(1, "Failed");
    }

    @Operation(summary = "修改用户信息")
    @Authorization
    @PostMapping(path = "/update-user-info")
    public R<Boolean> updateUserInfo(@RequestBody UserInfo userInfo,
                                     @Parameter(hidden = true) @CurrentUser User user) throws JsonProcessingException {
        Boolean result = userService.updateUserInfo(userInfo, user);
        return result ? R.success(result) : R.fail(1, "Failed");
    }

    @Operation(summary = "Apple登录")
    @PostMapping("/apple/login")
    public R<Login> appleLogin(@RequestBody AppleLoginDTO appleLoginDTO, HttpServletRequest request) {
        try {
            return R.success(userService.appleLogin(appleLoginDTO, request));
        } catch (Exception e) {
            log.error("Apple authorization login failed, please reauthorize", e);
            return R.fail(400, "Apple authorization login failed, please reauthorize");
        }
    }

    @Operation(summary = "用户账号删除")
    @Authorization
    @PostMapping(path = "/account-deletion")
    public R<Boolean> userAccountDeletion(@RequestBody UserDeleteDto userDeleteDto,
                                          @Parameter(hidden = true) @CurrentUser User user, HttpServletRequest request) {
        String token = request.getHeader(AUTHORIZATION);
        Boolean result = userService.userAccountDeletion(userDeleteDto, user, token);
        return result ? R.success(result) : R.fail(1, "Failed");
    }

    @Operation(summary = "用户注册(手机端)")
    @PostMapping(path = "/app-register")
    public R<Login> appRegister(@RequestBody Register register, HttpServletRequest request) {
        userService.register(register, request);
        return R.success(userService.login(register.getAccount(), register.getPassword()));
    }


    @Operation(summary = "校验用户注册的验证码是否正确")
    @PostMapping(path = "/register-check-vCode")
    public R<Boolean> registerCheckVCode(@RequestParam @Parameter(description = "验证码") String inviteCode,
                                         @RequestParam @Parameter(description = "用户登录名称") String loginName) {
        Boolean result = userService.registerCheckVCode(loginName, inviteCode);
        return result ? R.success(result) : R.fail(1, "Failed");
    }

    @Operation(summary = "校验用户忘记密码的验证码是否正确")
    @PostMapping(path = "/reset-check-vCode")
    public R<Boolean> resetCheckVCode(@RequestParam @Parameter(description = "验证码") String inviteCode,
                                      @RequestParam @Parameter(description = "用户登录名称") String loginName) {
        Boolean result = userService.resetCheckVCode(loginName, inviteCode);
        return result ? R.success(result) : R.fail(1, "Failed");
    }


    @Operation(summary = "查询用户信息(包含社区和lumen币)")
    @GetMapping("/select-user-detail")
    @Authorization
    public R<UserMessageDetail> getUserMessageDetail(@CurrentUser @Parameter(hidden = true) User user) {
        return R.success(userService.getUserMessageDetail(user));
    }

}
