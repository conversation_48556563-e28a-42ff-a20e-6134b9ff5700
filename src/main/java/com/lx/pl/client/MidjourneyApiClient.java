package com.lx.pl.client;

import com.lx.pl.dto.midjourney.MidjourneyRequest;
import com.lx.pl.dto.midjourney.MidjourneyResponse;
import retrofit2.Call;
import retrofit2.http.*;

/**
 * Midjourney API客户端接口
 *
 * <AUTHOR>
 */
public interface MidjourneyApiClient {

    /**
     * 生成图像 - /imagine
     */
    @POST("/midjourney/v1/imagine")
    Call<MidjourneyResponse> imagine(
            @Header("TT-API-KEY") String apiKey,
            @Body MidjourneyRequest.ImagineRequest request
    );

    /**
     * 图像变化 - U1~U4、V1~V4等操作
     */
    @POST("/midjourney/v1/action")
    Call<MidjourneyResponse> action(
            @Header("TT-API-KEY") String apiKey,
            @Body MidjourneyRequest.ActionRequest request
    );

    /**
     * 获取图像种子
     */
    @POST("/midjourney/v1/seed")
    Call<MidjourneyResponse> seed(
            @Header("TT-API-KEY") String apiKey,
            @Body MidjourneyRequest.SeedRequest request
    );

    /**
     * 图像合成 - /blend
     */
    @POST("/midjourney/v1/blend")
    Call<MidjourneyResponse> blend(
            @Header("TT-API-KEY") String apiKey,
            @Body MidjourneyRequest.BlendRequest request
    );

    /**
     * 图像描述 - /describe
     */
    @POST("/midjourney/v1/describe")
    Call<MidjourneyResponse> describe(
            @Header("TT-API-KEY") String apiKey,
            @Body MidjourneyRequest.DescribeRequest request
    );

    /**
     * 区域重绘 - Vary(region)
     */
    @POST("/midjourney/v1/inpaint")
    Call<MidjourneyResponse> inpaint(
            @Header("TT-API-KEY") String apiKey,
            @Body MidjourneyRequest.InpaintRequest request
    );

    /**
     * 获取任务状态 - fetch
     */
    @POST("/midjourney/v1/fetch")
    Call<MidjourneyResponse.TaskStatusResponse> fetch(
            @Header("TT-API-KEY") String apiKey,
            @Body MidjourneyRequest.FetchRequest request
    );

    /**
     * Prompt效验
     */
    @POST("/midjourney/v1/promptCheck")
    Call<MidjourneyResponse.PromptCheckResponse> promptCheck(
            @Header("TT-API-KEY") String apiKey,
            @Body MidjourneyRequest.PromptCheckRequest request
    );

    /**
     * Midjourney服务状态查询
     */
    @GET("/midjourney/status")
    Call<MidjourneyResponse.ServiceStatusResponse> getServiceStatus(
            @Header("TT-API-KEY") String apiKey
    );
}
