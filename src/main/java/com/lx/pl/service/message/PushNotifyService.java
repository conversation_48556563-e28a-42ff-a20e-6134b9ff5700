package com.lx.pl.service.message;

import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.lx.pl.constant.LogicConstants;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.db.mysql.gen.mapper.UserMapper;
import com.lx.pl.db.mysql.message.entity.PushMessage;
import com.lx.pl.db.mysql.message.entity.PushMessageContent;
import com.lx.pl.db.mysql.message.entity.PushOperateRecord;
import com.lx.pl.db.mysql.message.mapper.PushMessageContentMapper;
import com.lx.pl.db.mysql.message.mapper.PushMessageMapper;
import com.lx.pl.db.mysql.message.mapper.PushOperateRecordMapper;
import com.lx.pl.dto.message.SendNotificationCountDTO;
import com.lx.pl.dto.message.SendNotificationDTO;
import com.lx.pl.enums.*;
import com.lx.pl.service.RedisService;
import com.lx.pl.vo.PushMessageReportReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PushNotifyService {
    @Resource
    private SendNotificationFactory sendNotificationFactory;
    @Resource
    private PushMessageMapper pushMessageMapper;
    @Resource
    private PushMessageContentMapper pushMessageContentMapper;
    @Resource
    private PushOperateRecordMapper pushOperateRecordMapper;
    @Resource
    private DingTalkAlert dingTalkAlert;
    @Resource
    private RedisService redisService;
    @Resource
    private UserMapper userMapper;
    @Resource
    private RedissonClient redissonClient;


    /**
     * 轮询处理需要发送的消息
     */
    public void getAndExecute(int autoPush, LocalDateTime pushTime) {
        // 获取待处理的消息
        List<PushMessage> pushMessages = getAndUpdatePendingMessage(autoPush, pushTime);
        if (CollectionUtils.isEmpty(pushMessages)) {
            return;
        }

        pushMessages.forEach(this::getDetailAndExecute);
    }

    private void getDetailAndExecute(PushMessage pushMessage) {
        // 2.获取消息内容
        List<PushMessageContent> pushMessageContentPOS = pushMessageContentMapper.selectList(
                new LambdaQueryWrapper<PushMessageContent>()
                        .eq(PushMessageContent::getPushMessageId, pushMessage.getId())
        );

        // 获取默认英语内容
        PushMessageContent defaultContent = pushMessageContentPOS.stream()
                .filter(content -> Objects.equals(PushLanguageCodeEnum.ENGLISH.getCode(), content.getLanguageCode()))
                .findFirst()
                .orElseThrow(() -> new RuntimeException("English content not found"));

        // 3.处理推送范围
        List<String> countryList = StringUtils.isBlank(pushMessage.getPushScope()) ? null : Arrays.asList(pushMessage.getPushScope().split(","));

        // 4.分批处理推送
        final int BATCH_SIZE = 200;
        Long lastId = 0L;
        Object lastIdObj = redisService.get(LogicConstants.PUSH_LAST_ID_KEY_PREFIX + pushMessage.getId());
        if (lastIdObj != null) {
            lastId = Long.parseLong(lastIdObj.toString());
        }
        SendNotificationCountDTO sendNotificationCountDTO = new SendNotificationCountDTO();

        while (true) {
            List<User> batch = getPushUserBatch(lastId, BATCH_SIZE);
            if (CollectionUtils.isEmpty(batch)) {
                redisService.delete(LogicConstants.PUSH_LAST_ID_KEY_PREFIX + pushMessage.getId());
                break;
            }

            List<User> filterUsers = filterCountryAndToken(batch, countryList);
            if (CollectionUtils.isNotEmpty(filterUsers)) {
                // 处理推送
                processPlatformGroups(filterUsers, defaultContent.getTitle(), defaultContent.getBody(),
                        pushMessage, pushMessageContentPOS, sendNotificationCountDTO);
            }

            lastId = batch.get(batch.size() - 1).getId();
            redisService.set(LogicConstants.PUSH_LAST_ID_KEY_PREFIX + pushMessage.getId(), lastId);
        }

        // 5.更新消息状态和记录
        updateMessageStatus(pushMessage, sendNotificationCountDTO);

        // 发送钉钉告警
        if (sendNotificationCountDTO.getSuccessCount() +
                sendNotificationCountDTO.getFailureCount() +
                sendNotificationCountDTO.getLimitCount() > 0) {
            sendDingTalkAlert(defaultContent.getTitle(), sendNotificationCountDTO);
        }
    }

    private List<User> filterCountryAndToken(List<User> users, List<String> countryList) {
        List<User> filterUsers = null;
        for (User user : users) {
            if (StringUtils.isBlank(user.getIosFcmToken()) && StringUtils.isBlank(user.getAndroidFcmToken())) {
                continue;
            }

            if (CollectionUtils.isNotEmpty(countryList)) {
                if (StringUtils.isBlank(user.getRegistCountry()) || !countryList.contains(user.getRegistCountry())) {
                    continue;
                }
            }

            if (filterUsers == null) {
                filterUsers = new ArrayList<>();
            }
            filterUsers.add(user);
        }

        return filterUsers;
    }

    /**
     * 处理消息打开上报
     */
    public void pushOpenReport(PushMessageReportReq req) {
        Long messageId = null;
        if (NumberUtil.isNumber(req.getMessageId())) {
            messageId = Long.valueOf(req.getMessageId());
        }
        if (messageId == null) {
            log.warn("PUSH打开消息上报失败, 未知的messageId: {}", messageId);
            return;
        }
        PushMessage pushMessage = pushMessageMapper.selectById(messageId);
        if (pushMessage == null) {
            log.warn("PUSH打开消息上报失败, 未知的messageId: {}", messageId);
            return;
        }
        String uvKey = LogicConstants.PUSH_OPEN_KEY_PREFIX + messageId;
        boolean newKey = redisService.hasKey(uvKey);
        // 增加PUSH消息打开数
        redisService.increment(uvKey);
        if (newKey) {
            // 新UV key设置过期时间
            redisService.expire(uvKey, LogicConstants.PUSH_OPEN_EXPIRED_TIME, TimeUnit.HOURS);
        }
    }

    // 辅助方法
    private List<User> getPushUserBatch(Long lastId, int batchSize) {
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(
                User::getId,
                User::getRegistCountry,
                User::getAndroidFcmToken,
                User::getIosFcmToken,
                User::getLocalLang
        );
        queryWrapper
                .gt(User::getId, lastId)
                .orderByAsc(User::getId)
                .last("limit " + batchSize);

        return userMapper.selectList(queryWrapper);
    }

    private void processPlatformGroups(List<User> batch, String defaultTitle, String defaultBody,
                                       PushMessage pushMessage, List<PushMessageContent> messageContents,
                                       SendNotificationCountDTO sendNotificationCountDTO) {
        Map<String, List<User>> platformGroups = groupByPlatform(batch);

        platformGroups.forEach((platform, users) -> {
            try {
                Map<String, List<User>> languageGroups = users.stream().collect(Collectors.groupingBy(user -> StringUtils.isNotBlank(user.getLocalLang()) ?
                        user.getLocalLang() : PushLanguageCodeEnum.ENGLISH.getCode()));

                languageGroups.forEach((languageCode, usersInLanguage) -> {
                    String finalTitle = defaultTitle;
                    String finalBody = defaultBody;

                    // 如果有多语言内容，查找对应语言的内容
                    if (messageContents != null) {
                        PushMessageContent languageContent = messageContents.stream()
                                .filter(content -> Objects.equals(languageCode, content.getLanguageCode()))
                                .findFirst()
                                .orElse(null);

                        if (languageContent != null) {
                            finalTitle = languageContent.getTitle();
                            finalBody = languageContent.getBody();
                        }
                    }

                    sendNotificationToUsers(usersInLanguage, platform, finalTitle, finalBody,
                            pushMessage, sendNotificationCountDTO);
                });
            } catch (Exception e) {
                log.error("Failed to send notifications for platform: {}, error: {}", platform, e.getMessage());
            }
        });
    }

    private void sendNotificationToUsers(List<User> users, String platform,
                                         String title, String body, PushMessage pushMessage,
                                         SendNotificationCountDTO sendNotificationCountDTO) {

        // 获取符合条件用户的FCM tokens
        List<String> fcmTokens;
        if (ClientType.ios.getValue().equals(platform)) {
            fcmTokens = users.stream()
                    .map(User::getIosFcmToken)
                    .filter(StringUtils::isNotBlank).distinct()
                    .collect(Collectors.toList());
        } else {
            fcmTokens = users.stream()
                    .map(User::getAndroidFcmToken)
                    .filter(StringUtils::isNotBlank).distinct()
                    .collect(Collectors.toList());
        }

        if (CollectionUtils.isNotEmpty(fcmTokens)) {
            ISendNotification sendNotification = sendNotificationFactory.findSendNotification(
                    ClientType.getByValue(platform),
                    NotificationTokenTypeEnum.FCM_TOKEN
            );

            if (sendNotification != null) {
                SendNotificationDTO.SendNotificationDTOBuilder builder = SendNotificationDTO.builder()
                        .targetTokenList(fcmTokens)
                        .pushType(PushTypeEnum.TEXT)
                        .title(title)
                        .body(body);

                // 如果是消息推送任务，添加额外参数
                if (pushMessage != null) {
                    builder.pushType(PushTypeEnum.fromValue(pushMessage.getPushType()))
                            .schemeEnum(SchemeEnum.getByToolType(pushMessage.getSchemeUrl()))
                            .messageId(pushMessage.getId())
                            .imageUrl(pushMessage.getPicUrl())
                            .thumbnailUrl(pushMessage.getThumbnailUrl());
                }

                sendNotification.sendNotification(builder.build(), sendNotificationCountDTO);
            }
        }
    }

    private Map<String, List<User>> groupByPlatform(List<User> users) {
        Map<String, List<User>> platformGroups = new HashMap<>();
        for (User user : users) {
            if (StringUtils.isNotBlank(user.getIosFcmToken())) {
                platformGroups.computeIfAbsent(ClientType.ios.getValue(), k -> new ArrayList<>()).add(user);
            }

            if (StringUtils.isNotBlank(user.getAndroidFcmToken())) {
                platformGroups.computeIfAbsent(ClientType.android.getValue(), k -> new ArrayList<>()).add(user);
            }
        }

        return platformGroups;
    }

    private List<PushMessage> getAndUpdatePendingMessage(int autoPush, LocalDateTime pushTime) {
        LambdaQueryWrapper<PushMessage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PushMessage::getStatus, PushMessageStatusEnum.PENDING.getCode())
                .eq(PushMessage::getDailyAutoPush, autoPush);
        if (pushTime != null) {
            queryWrapper.le(PushMessage::getPushTime, LocalDateTime.now());
        }
        queryWrapper.orderByAsc(PushMessage::getId);

        List<PushMessage> pushMessages = pushMessageMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(pushMessages)) {
            return null;
        }

        PushMessage updateMessage = new PushMessage();
        updateMessage.setStatus(PushMessageStatusEnum.PUSHING.getCode());
        pushMessageMapper.update(updateMessage,
                new LambdaUpdateWrapper<>(PushMessage.class).in(PushMessage::getId, pushMessages.stream().map(PushMessage::getId).collect(Collectors.toList())));

        return pushMessages;
    }

    private void updateMessageStatus(PushMessage pushMessage, SendNotificationCountDTO sendNotificationCountDTO) {
        // 记录推送数量
        PushOperateRecord pushOperateRecordPO = new PushOperateRecord();
        pushOperateRecordPO.setPushMessageId(pushMessage.getId());
        pushOperateRecordPO.setPushCount(sendNotificationCountDTO.getSuccessCount());
        pushOperateRecordMapper.insert(pushOperateRecordPO);

        // 更新消息状态为已推送
        PushMessage updateMessage = new PushMessage();
        updateMessage.setId(pushMessage.getId());
        updateMessage.setStatus(PushMessageStatusEnum.PUSHED.getCode());
        pushMessageMapper.updateById(updateMessage);
    }

    private void sendDingTalkAlert(String title, SendNotificationCountDTO sendNotificationCountDTO) {
        String dingTalkMessage = String.format(
                "Push message Title: %s, Success Count: %d, Failure Count: %d, Limit Count: %d, TimeElapsed: %dms",
                title,
                sendNotificationCountDTO.getSuccessCount(),
                sendNotificationCountDTO.getFailureCount(),
                sendNotificationCountDTO.getLimitCount(),
                sendNotificationCountDTO.getTimeElapsed()
        );
        dingTalkAlert.send(dingTalkMessage);
    }

    //自动推送，每天UTC时间8点
    @Scheduled(cron = "0 0 8 * * ?", zone = "UTC")
    public void dailyPushExecute() {
        log.info("开始执行每日自动任务");
        if (redisService.hasKey(LogicConstants.DISABLE_AUTO_PUSH_KEY)) {
            log.info("自动推送已被禁用");
            return;
        }
        RLock lock = redissonClient.getLock(LogicConstants.DAILY_PUSH_EXECUTE_LOCK_KEY);
        try {
            if (!lock.tryLock()) {
                //其他实例正在执行
                return;
            }
            getAndExecute(1, null);
        } catch (Exception e) {
            log.error("执行每日自动PUSH任务异常, ", e);
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }
}
