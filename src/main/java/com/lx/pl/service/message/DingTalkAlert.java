package com.lx.pl.service.message;


import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiRobotSendRequest;
import com.dingtalk.api.response.OapiRobotSendResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.concurrent.CompletableFuture;


@Service
@Slf4j
public class DingTalkAlert {

    @Value("${spring.profiles.active:default}")
    private String env;


    @Resource
    private  ThreadPoolTaskExecutor dingTalkAlertExecutor;


    public static final String CUSTOM_ROBOT_TOKEN = "dfc4765466c009012eb929448aadbd9bec1b14c892a0ce2125df97bd94418a18";

    //告警必须包含此前缀才能发送
    private static final String ALERT_PREFIX = "PUSH推送告警";


    /**
     * 默认异步发送， 机器人每分钟最多发送20条消息 超过20条 限流10分钟
     * @param alertMsg 告警消息
     */
    public void send(String alertMsg){
        CompletableFuture.runAsync(() -> {
            try {
                // 只有生产环境告警
                /*if(!Objects.equals(aieaseProperties.getEnv(), "prod")){
                    return;
                }*/
                //sign字段和timestamp字段必须拼接到请求URL上，否则会出现 310000 的错误信息 -> 我就没设置过加密的方式
                DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/robot/send");
                OapiRobotSendRequest req = new OapiRobotSendRequest();
                //定义文本内容
                OapiRobotSendRequest.Text text = new OapiRobotSendRequest.Text();
                text.setContent(ALERT_PREFIX + "[" + env +  "]:" +   alertMsg);
                //设置消息类型
                req.setMsgtype("text");
                req.setText(text);
                OapiRobotSendResponse execute = client.execute(req, CUSTOM_ROBOT_TOKEN);
            } catch (Exception e) {
                log.error("DingTalk send msg error", e);
            }
        }, dingTalkAlertExecutor);
    }


}
