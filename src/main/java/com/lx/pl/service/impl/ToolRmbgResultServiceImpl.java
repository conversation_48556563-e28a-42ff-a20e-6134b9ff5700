package com.lx.pl.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lx.pl.db.mysql.gen.entity.ToolRmbgResult;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.db.mysql.gen.mapper.ToolRmbgResultMapper;
import com.lx.pl.dto.ToolRmbgParam;
import com.lx.pl.dto.mq.RmbgVo;
import com.lx.pl.enums.LogicErrorCode;
import com.lx.pl.mq.message.CommonMqMessage;
import com.lx.pl.mq.message.RMqMessage;
import com.lx.pl.mq.producer.NormalMessageProducer;
import com.lx.pl.service.ToolRmbgResultService;
import com.lx.pl.service.UserService;
import com.lx.pl.service.VipService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

@Service
public class ToolRmbgResultServiceImpl extends ServiceImpl<ToolRmbgResultMapper, ToolRmbgResult> implements ToolRmbgResultService {

    Logger log = LoggerFactory.getLogger(ToolRmbgResultServiceImpl.class);
    @Resource
    private NormalMessageProducer<RmbgVo> normalMessageProducer;
    @Value("${rocketmq.piclumen.rmbg.tag:tag_rmbg_dev}")
    private String tag;
    @Value("${rocketmq.piclumen.rmbg.topic:tp_rmbg_dev}")
    private String topic;
    @Resource
    private VipService vipService;
    @Resource
    private UserService userService;
    //允许处理中的总任务数
    private static final Integer TOTAL_MAX_TASKS = 400;
    //允许处理中的个人任务数
    private static final Integer PERSONAL_MAX_TASKS = 30;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ToolRmbgResult saveRmbgFile(ToolRmbgParam param, User user, String platform) {
        ToolRmbgResult toolRmbgResult = new ToolRmbgResult();
        toolRmbgResult.setUpFileUrl(param.getFileUrl());
        toolRmbgResult.setUpWidth(param.getWidth());
        toolRmbgResult.setUpHeight(param.getHeight());
        toolRmbgResult.setUpTime(LocalDateTime.now());
        toolRmbgResult.setUserId(user.getId());
        toolRmbgResult.setLoginName(user.getLoginName());
        toolRmbgResult.setBatchId(param.getBatchId());
        toolRmbgResult.setStatus(1);
        this.save(toolRmbgResult);
        RmbgVo rmbgVo = new RmbgVo();
        rmbgVo.setTaskId(toolRmbgResult.getId());
        rmbgVo.setSrcUrl(param.getFileUrl());
        rmbgVo.setUserId(user.getId());
        CommonMqMessage<RmbgVo> commonMqMessage = new RMqMessage<>(topic, tag, toolRmbgResult.getId().toString());
        commonMqMessage.setMessage(rmbgVo);
        normalMessageProducer.syncSend(commonMqMessage);
        return toolRmbgResult;
    }

    @Override
    public void dealRmbgReadyStatus(RmbgVo readyServerVo) {
        if (readyServerVo.getTaskId() == null) {
            log.warn("taskId为空");
            return;
        }
        if (readyServerVo.getAfterUrl() == null) {
            log.warn("afterUrl为空");
            this.lambdaUpdate().eq(ToolRmbgResult::getId, readyServerVo.getTaskId())
                    .set(ToolRmbgResult::getStatus, 3)
                    .set(ToolRmbgResult::getFailureMessage, readyServerVo.getFailureMessage())
                    .update();
        } else {
            this.lambdaUpdate().eq(ToolRmbgResult::getId, readyServerVo.getTaskId())
                    .set(ToolRmbgResult::getFileUrl, readyServerVo.getAfterUrl())
                    .set(ToolRmbgResult::getStatus, 2)
                    .update();

            //扣点
            User user = userService.getUserById(readyServerVo.getUserId());
            vipService.dealUserLumensCost(1, "", 1, user);
        }
    }

    @Override
    public List<ToolRmbgResult> getRmbgHistory(User user) {
        //查询最新的30条数据
        LocalDateTime startTime = LocalDateTime.now().minusDays(1);
        return this.lambdaQuery().eq(ToolRmbgResult::getUserId, user.getId()).gt(ToolRmbgResult::getCreateTime, startTime)
                .orderByDesc(ToolRmbgResult::getId)
                .last("limit 30").list();
    }

    @Override
    public List<ToolRmbgResult> getRmbgResultStatus(List<Long> ids) {
        //通过ids查询数据
        if (ids != null && ids.size() > 0) {
            return this.lambdaQuery().in(ToolRmbgResult::getId, ids).list();
        }
        return null;
    }

    @Override
    public void deleteBatchByIds(User user, List<Long> ids) {
        //通过ids删除数据
        if (ids != null && ids.size() > 0) {
            this.lambdaUpdate().eq(ToolRmbgResult::getUserId, user.getId()).in(ToolRmbgResult::getId, ids).remove();
        }
    }

    @Override
    public LogicErrorCode passVerify(User user, String batchId) {
        if (StringUtils.isBlank(batchId)) {
            return LogicErrorCode.BATCH_RMBG_BATCH_ID_EMPTY_ERROR;
        }

        //先判断个人任务数是否已达到上限
        long personal = this.count(new LambdaQueryWrapper<>(ToolRmbgResult.class)
                .eq(ToolRmbgResult::getBatchId, batchId)
                .eq(ToolRmbgResult::getUserId, user.getId()));
        if (personal > PERSONAL_MAX_TASKS) {
            return LogicErrorCode.BATCH_RMBG_TASK_LIMITED;
        }

        //判断总任务数是否已达到上限
        long all = this.count(new LambdaQueryWrapper<>(ToolRmbgResult.class)
                .eq(ToolRmbgResult::getStatus, 1));
        if (all >= TOTAL_MAX_TASKS && personal <= 0) {
            return LogicErrorCode.BATCH_RMBG_TASK_LIMITED;
        }

        //校验点数是否足够
        int surplusLumen = vipService.getUserSurplusLumens(user);
        if (surplusLumen < 1) {
            return LogicErrorCode.NOT_ENOUGH_LUMENS_SUPPORT;
        }
        return null;
    }


    // 基础的 CRUD 操作由 ServiceImpl 提供
}