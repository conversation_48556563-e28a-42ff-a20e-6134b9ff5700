package com.lx.pl.service;

import com.lx.pl.constant.LogicConstants;
import com.lx.pl.db.mysql.community.entity.*;
import com.lx.pl.db.mysql.gen.entity.GptPlatformActivity;
import com.lx.pl.db.mysql.gen.entity.GptSysUpdate;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.dto.CommMessageParams;
import com.lx.pl.dto.CommPageInfo;
import com.lx.pl.dto.CommUserMessageNums;
import com.lx.pl.enums.MessageType;
import com.lx.pl.enums.VipType;
import com.lx.pl.exception.ServerInternalException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.*;


@Service
@Slf4j
public class CommMessageService {

    public static final String COMM_USER_NOT_READ_MESSAGE = "COMM_USER_NOT_READ_MESSAGE" + ":";

    public static final String COMM_USER_READ_MESSAGE = "COMM_USER_READ_MESSAGE" + ":";


    /**
     * web系统更新总数
     */
    public static final String WEB_SYSUPDATE_NUMS = "WEB_SYSUPDATE_NUMS";

    /**
     * ios系统更新总数
     */
    public static final String IOS_SYSUPDATE_NUMS = "IOS_SYSUPDATE_NUMS";

    /**
     * 安卓系统更新总数
     */
    public static final String ANDROID_SYSUPDATE_NUMS = "ANDROID_SYSUPDATE_NUMS";

    /**
     * 会员系统活动公告总数，包含给所有用户和vip用户的公告活动
     */
    public static final String VIP_PLATFORM_ACTIVITY_NUMS = "VIP_PLATFORM_ACTIVITY_NUMS";

    /**
     * 非会员系统活动公告总数，包含给所有用户和非vip用户的公告活动
     */
    public static final String NOT_VIP_PLATFORM_ACTIVITY_NUMS = "NOT_VIP_PLATFORM_ACTIVITY_NUMS";


    public static final String NOT_READ_LIKE_NUMS = "NOT_READ_LIKE_NUMS";

    public static final String NOT_READ_COMMENT_NUMS = "NOT_READ_COMMENT_NUMS";

    public static final String NOT_READ_PLATFORM_MESSAGE_NUMS = "NOT_READ_PLATFORM_MESSAGE_NUMS";


    /**
     * 系统更新用户已读数
     */
    public static final String WEB_READ_SYSUPDATE_NUMS = "WEB_READ_SYSUPDATE_NUMS";

    public static final String IOS_READ_SYSUPDATE_NUMS = "IOS_READ_SYSUPDATE_NUMS";

    public static final String ANDROID_READ_SYSUPDATE_NUMS = "ANDROID_READ_SYSUPDATE_NUMS";

    /**
     * 已读会员系统活动公告，包含给所有用户和vip用户的公告活动
     */
    public static final String READ_VIP_PLATFORM_ACTIVITY_NUMS = "READ_VIP_PLATFORM_ACTIVITY_NUMS";

    /**
     * 已读非会员系统活动公告，包含给所有用户和非vip用户的公告活动
     */
    public static final String READ_NOT_VIP_PLATFORM_ACTIVITY_NUMS = "READ_NOT_VIP_PLATFORM_ACTIVITY_NUMS";

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private RedisService redisService;

    @Lazy
    @Autowired
    private CommLikeService commLikeService;

    @Lazy
    @Autowired
    private CommCommentService commCommentService;

    @Autowired
    private PlatformActivityService platformActivityService;

    @Autowired
    private PlatformMessageService platformMessageService;

    @Autowired
    private SysUpdateService sysUpdateService;


    public CommUserMessageNums getCommUserMessageNums(User user,String platform) {
        CommUserMessageNums commUserMessageNums = new CommUserMessageNums();
        String loginName = user.getLoginName();

        try {
            //获取用户未读消息
            Map<String, Object> notReadResultMap = redisService.getHashAsMap(COMM_USER_NOT_READ_MESSAGE + loginName);
            int nLikeNums = (int) Optional.ofNullable(notReadResultMap.get(NOT_READ_LIKE_NUMS)).orElse(0);
            int nCommentNums = (int) Optional.ofNullable(notReadResultMap.get(NOT_READ_COMMENT_NUMS)).orElse(0);
            int nPlatformMessageNums = (int) Optional.ofNullable(notReadResultMap.get(NOT_READ_PLATFORM_MESSAGE_NUMS)).orElse(0);


            //获取用户已读消息
            Map<String, Object> readResultMap = redisService.getHashAsMap(COMM_USER_READ_MESSAGE + loginName);
            int readSysUpdateNums = getReadSysUpdateNums(platform,readResultMap);
            //包含给所有用户和vip用户的公告活动
            int readVipPlatformActivityNums = (int) Optional.ofNullable(readResultMap.get(READ_VIP_PLATFORM_ACTIVITY_NUMS)).orElse(0);
            //包含给所有用户和非vip用户的公告活动
            int readNotVipPlatformActivityNums = (int) Optional.ofNullable(readResultMap.get(READ_NOT_VIP_PLATFORM_ACTIVITY_NUMS)).orElse(0);


            //获取平台活动和系统更新总数
            //系统更新总数
            int sysupdateNums = getSysupdateNums(platform);
            //平台活动vip用户活动总数
            int vipPlatformActivityNums = (int) Optional.ofNullable(redisService.get(VIP_PLATFORM_ACTIVITY_NUMS)).orElse(0);
            //平台活动非vip用户活动总数
            int notVipPlatformActivityNums = (int) Optional.ofNullable(redisService.get(NOT_VIP_PLATFORM_ACTIVITY_NUMS)).orElse(0);

            // 获取社区活动公共数量
            Set<Long> commActivityIds = redisService.getDataFromSet(LogicConstants.COMM_ACTIVITY_PUBLIC);

            // 获取用户已查看活动数量
            int userCommActivityNum = (int) Optional.ofNullable(redisService.getDataFromHash(LogicConstants.COMM_ACTIVITY_USER_NUM,  loginName)).orElse(0);

            // 用户未读社区活动信息数量
            Integer  commActivityNums = 0;
            if (CollectionUtils.isNotEmpty(commActivityIds) && userCommActivityNum <= commActivityIds.size()) {
                // do something
                commActivityNums = commActivityIds.size() - userCommActivityNum;
            }

            //(系统更新和平台活动)未读数 = 总数 - 已读数
            int nPlatformActivityNums = 0;
            if (VipType.basic.getValue().equals(user.getVipType())) {
                nPlatformActivityNums = notVipPlatformActivityNums - readNotVipPlatformActivityNums;
            } else {
                nPlatformActivityNums = vipPlatformActivityNums - readVipPlatformActivityNums;
            }
            //未读系统更新总数
            int nSysUpdateNums = sysupdateNums - readSysUpdateNums;

            /**
             * 避免脏数据出现负值
             */
            nLikeNums = nLikeNums >= 0 ? nLikeNums : 0;
            nCommentNums = nCommentNums >= 0 ? nCommentNums : 0;
            nSysUpdateNums = nSysUpdateNums >= 0 ? nSysUpdateNums : 0;
            nPlatformMessageNums = nPlatformMessageNums >= 0 ? nPlatformMessageNums : 0;
            nPlatformActivityNums = nPlatformActivityNums >= 0 ? nPlatformActivityNums : 0;

            //将未读信息值返回
            commUserMessageNums.setCommActivityNums(commActivityNums);
            commUserMessageNums.setNLikeNums(nLikeNums);
            commUserMessageNums.setNCommentNums(nCommentNums);
            commUserMessageNums.setNSysUpdateNums(nSysUpdateNums);
            commUserMessageNums.setNPlatformMessageNums(nPlatformMessageNums);
            commUserMessageNums.setNPlatformActivityNums(nPlatformActivityNums);
            commUserMessageNums.setNMessageTotalNums(nLikeNums + nCommentNums + nSysUpdateNums + nPlatformMessageNums + nPlatformActivityNums);

        } catch (Exception e) {
            log.error("用户：{},查询未读消息 失败", loginName, e);
            throw new ServerInternalException("get not read Message Failed");
        }

        return commUserMessageNums;
    }

    public Boolean reduceCommUserMessageNums(CommMessageParams commMessageParams, User user ,String platform) {

        //判断是否是点赞还是评论还是系统更新还是平台信息还是平台活动
        if (!Objects.isNull(commMessageParams.getMessageType())) {
            switch (MessageType.fromValue(commMessageParams.getMessageType())) {
                case LIKE:
                    dealLike(user, commMessageParams);
                    break;
                case COMMENT:
                    dealComment(user, commMessageParams);
                    break;
                case SYS_UPDATE:
                    dealSysUpdate(user, commMessageParams ,platform);
                    break;
                case PLATFORM_MESSAGE:
                    dealPlatformMessage(user, commMessageParams);
                    break;
                case PLATFORM_ACTIVITY:
                    dealPlatformActivity(user, commMessageParams);
                    break;
            }
        }

        return Boolean.TRUE;
    }

    //处理点赞已读
    public void dealLike(User user, CommMessageParams commMessageParams) {
        decrementFieldSafely(COMM_USER_NOT_READ_MESSAGE + user.getLoginName(), NOT_READ_LIKE_NUMS);
        commLikeService.readCommLike(commMessageParams.getMessageId(), user);
    }

    //处理评论已读
    public void dealComment(User user, CommMessageParams commMessageParams) {
        decrementFieldSafely(COMM_USER_NOT_READ_MESSAGE + user.getLoginName(), NOT_READ_COMMENT_NUMS);
        commCommentService.readCommComment(commMessageParams.getMessageId(), user);
    }

    //处理系统消息已读
    public void dealSysUpdate(User user, CommMessageParams commMessageParams,String platform) {
        switch (platform){
            case "web":
                redisService.incrementFieldInHash(COMM_USER_READ_MESSAGE + user.getLoginName(), WEB_READ_SYSUPDATE_NUMS, 1);
                break;
            case "ios":
                redisService.incrementFieldInHash(COMM_USER_READ_MESSAGE + user.getLoginName(), IOS_READ_SYSUPDATE_NUMS, 1);
                break;
            case "android":
                redisService.incrementFieldInHash(COMM_USER_READ_MESSAGE + user.getLoginName(), ANDROID_READ_SYSUPDATE_NUMS, 1);
                break;
        }
        sysUpdateService.readSysUpdate(commMessageParams.getMessageId(), user, platform);
    }

    //处理平台消息已读
    public void dealPlatformMessage(User user, CommMessageParams commMessageParams) {
        decrementFieldSafely(COMM_USER_NOT_READ_MESSAGE + user.getLoginName(), NOT_READ_PLATFORM_MESSAGE_NUMS);
        platformMessageService.readPlatformMessage(commMessageParams.getMessageId(), user);
    }

    //处理平台活动已读
    public void dealPlatformActivity(User user, CommMessageParams commMessageParams) {
        if (VipType.basic.getValue().equals(user.getVipType())) {
            redisService.incrementFieldInHash(COMM_USER_READ_MESSAGE + user.getLoginName(), READ_NOT_VIP_PLATFORM_ACTIVITY_NUMS, 1);
        } else {
            redisService.incrementFieldInHash(COMM_USER_READ_MESSAGE + user.getLoginName(), READ_VIP_PLATFORM_ACTIVITY_NUMS, 1);
        }

        platformActivityService.readPlatformActivity(commMessageParams.getMessageId(), user);
    }

    public Boolean reduceReadAll(User user, String platform) {
        String loginName = user.getLoginName();
        String notReadNumsKey = COMM_USER_NOT_READ_MESSAGE + loginName;

        //采用延时双删策略
        redisService.unlink(notReadNumsKey);
        dealReadMessage(user, platform);

        //处理点赞已读的历史数据
        commLikeService.readCommLike("", user);
        //处理评论已经得历史数据
        commCommentService.readCommComment("", user);
        //处理系统消息已经得历史数据
        sysUpdateService.readSysUpdate("", user, platform);
        //处理平台消息已经得历史数据
        platformMessageService.readPlatformMessage("", user);
        //处理平台活动已经得历史数据
        platformActivityService.readPlatformActivity("", user);

        redisService.unlink(notReadNumsKey);
        dealReadMessage(user, platform);

        return Boolean.TRUE;
    }

    public void decrementFieldSafely(String key, String field) {
        Integer currentValue = (Integer) redisService.getDataFromHash(key, field);
        if (currentValue != null && currentValue > 0) {
            redisService.incrementFieldInHash(key, field, -1);
        }
    }

    public void dealReadMessage(User user, String platform) {
        //获取平台活动和系统更新总数
        //系统更新总数
        int sysupdateNums = getSysupdateNums(platform);
        //平台活动vip用户活动总数
        int vipPlatformActivityNums = (int) Optional.ofNullable(redisService.get(VIP_PLATFORM_ACTIVITY_NUMS)).orElse(0);
        //平台活动非vip用户活动总数
        int notVipPlatformActivityNums = (int) Optional.ofNullable(redisService.get(NOT_VIP_PLATFORM_ACTIVITY_NUMS)).orElse(0);

        //将总数更新到已读信息数
        Map<String, Integer> readMessageCounts = new HashMap<>();
        readMessageCounts.put(getSysUpdateNumRedisKey(platform), sysupdateNums);
        readMessageCounts.put(READ_VIP_PLATFORM_ACTIVITY_NUMS, vipPlatformActivityNums);
        readMessageCounts.put(READ_NOT_VIP_PLATFORM_ACTIVITY_NUMS, notVipPlatformActivityNums);

        redisService.putAllToHash(COMM_USER_READ_MESSAGE + user.getLoginName(), readMessageCounts);

    }


    public CommPageInfo<CommLike> getLikeMessage(String lastLikeId, Integer pageSize, User user) {
        return commLikeService.getLikeMessage(lastLikeId, pageSize, user);
    }

    public CommPageInfo<CommComment> getCommentMessage(String lastCommentId, Integer pageSize, User user) {
        return commCommentService.getCommentMessage(lastCommentId, pageSize, user);
    }

    public CommPageInfo<GptSysUpdate> getSysUpdateMessage(String lastSysUpdateId, Integer pageSize, User user, String platform) {
        return sysUpdateService.getSysUpdateMessage(lastSysUpdateId, pageSize, user, platform);
    }

    public CommPageInfo<GptPlatformActivity> getActivityMessage(String lastActivityId, Integer pageSize, User user) {
        return platformActivityService.getActivityMessage(lastActivityId, pageSize, user);
    }

    public CommPageInfo<UserPlatformMessage> getPlatformMessage(String lastPlatformMessId, Integer pageSize, User user) {
        return platformMessageService.getPlatformMessage(lastPlatformMessId, pageSize, user);
    }

    public Integer getReadSysUpdateNums(String platform ,Map<String, Object> readResultMap){
        Integer readSysUpdateNums = 0;
        switch (platform){
            case "web":
                readSysUpdateNums =  (int) Optional.ofNullable(readResultMap.get(WEB_READ_SYSUPDATE_NUMS)).orElse(0);
                break;
            case "ios":
                readSysUpdateNums =  (int) Optional.ofNullable(readResultMap.get(IOS_READ_SYSUPDATE_NUMS)).orElse(0);
                break;
            case "android":
                readSysUpdateNums =  (int) Optional.ofNullable(readResultMap.get(ANDROID_READ_SYSUPDATE_NUMS)).orElse(0);
                break;
        }
        return readSysUpdateNums;
    }

    public Integer getSysupdateNums(String platform){
        Integer sysupdateNums = 0;
        switch (platform){
            case "web":
                sysupdateNums = (int) Optional.ofNullable(redisService.get(WEB_SYSUPDATE_NUMS)).orElse(0);
                break;
            case "ios":
                sysupdateNums = (int) Optional.ofNullable(redisService.get(IOS_SYSUPDATE_NUMS)).orElse(0);
                break;
            case "android":
                sysupdateNums =  (int) Optional.ofNullable(redisService.get(ANDROID_SYSUPDATE_NUMS)).orElse(0);
                break;
        }
        return sysupdateNums;
    }

    public String getSysUpdateNumRedisKey(String platform){

        // 默认为web
        String sysUpdateNumRedisKey = WEB_READ_SYSUPDATE_NUMS;
        switch (platform){
            case "web":
                sysUpdateNumRedisKey = WEB_READ_SYSUPDATE_NUMS;
                break;
            case "ios":
                sysUpdateNumRedisKey = IOS_READ_SYSUPDATE_NUMS;
                break;
            case "android":
                sysUpdateNumRedisKey = ANDROID_READ_SYSUPDATE_NUMS;
                break;
        }
        return sysUpdateNumRedisKey;
    }


}
