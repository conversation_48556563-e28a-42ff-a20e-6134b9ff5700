package com.lx.pl.service;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.lx.pl.constant.LogicConstants;
import com.lx.pl.db.mysql.gen.entity.PromptFile;
import com.lx.pl.db.mysql.gen.entity.PromptRecord;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.db.mysql.gen.entity.VipStandards;
import com.lx.pl.db.mysql.gen.mapper.PromptFileMapper;
import com.lx.pl.db.mysql.gen.mapper.PromptRecordMapper;
import com.lx.pl.db.mysql.gen.mapper.UserMapper;
import com.lx.pl.db.mysql.gen.mapper.VipMapper;
import com.lx.pl.db.mysql.pay.entity.LogLumenCost;
import com.lx.pl.dto.GenGenericPara;
import com.lx.pl.dto.OldUserLumens;
import com.lx.pl.dto.UserLumens;
import com.lx.pl.enums.LogicErrorCode;
import com.lx.pl.enums.LumenType;
import com.lx.pl.enums.OriginCreate;
import com.lx.pl.enums.VipType;
import com.lx.pl.exception.LogicException;
import com.lx.pl.exception.ServerInternalException;
import com.lx.pl.pay.apple.VipPlatform;
import com.lx.pl.pay.common.domain.PayLumenRecord;
import com.lx.pl.pay.common.domain.SubscriptionCurrent;
import com.lx.pl.pay.common.mapper.PayLumenRecordMapper;
import com.lx.pl.pay.common.service.PayLumenRecordService;
import com.lx.pl.pay.common.service.SubscriptionCurrentService;
import com.lx.pl.util.DateUtils;
import com.lx.pl.util.DoubleMathUtils;
import com.lx.pl.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.stream.Collectors;

import static com.lx.pl.constant.LockPrefixConstant.FAST_CREATE_LOCK_PREFIX;
import static com.lx.pl.constant.LockPrefixConstant.OPERATE_PERMISSION_LOCK_PREFIX;

@Service
@Slf4j
public class VipService {

    @Value("${realistic.ModelId}")
    String realisticModelId;

    @Value("${anime.ModelId}")
    String animeModelId;

    @Value("${lineart.ModelId}")
    String lineartModelId;

    @Value("${pony.ModelId}")
    String ponyModelId;

    @Value("${fluxschell.modelId}")
    String fluxschellModelId;

    @Value("${fluxdev.modelId}")
    String fluxdevModelId;

    @Value("${art.ModelId}")
    String artModelId;

    public static final String USER_RECHARGE_TOTAL_LUMENS = "user_recharge_total_lumens"; // 用户目前充值的有效总点数

    public static final String USER_VIP_TOTAL_LUMENS = "user_vip_total_lumens"; // 用户目前vip赠送的有效总点数

    public static final String USER_RECHARGE_USE_LUMENS = "user_recharge_use_lumens"; // 用户目前充值的已经使用点数

    public static final String USER_VIP_USE_LUMENS = "user_vip_use_lumens"; // 用户目前vip赠送的已使用点数

    public static final String USER_GIFT_TOTAL_LUMENS = "user_gift_total_lumens"; // 用户目前赠送的有效总点数

    public static final String USER_GIFT_USE_LUMENS = "user_gift_use_lumens"; // 用户目前赠送的已经使用点数

    public static final String USER_NOT_FINISH_TASK_LUMENS = "user_not_finish_task_lumens"; // 用户的未完成的任务点数

    public static final String USER_VIP_STANDARDS = "user_vip_standards"; // 用vip的权限标准

    public static final String USER_TODAY_RELAX_CREATE_IMG_NUMS = "user_today_relax_create_img_nums";  //用户当天relax生图数量

    @Autowired
    VipMapper vipStandardsMapper;

    @Autowired
    UserMapper userMapper;

    @Autowired
    PromptRecordMapper promptRecordMapper;

    @Autowired
    PayLumenRecordMapper payLumenRecordMapper;

    @Autowired
    RedisService redisService;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    PayLumenRecordService payLumenRecordService;

    @Autowired
    private RedissonClient redissonClient;

    @Lazy
    @Autowired
    private GenService genService;

    @Autowired
    private SubscriptionCurrentService subscriptionCurrentService;

    @Autowired
    private LumenTaskService lumenTaskService;

    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private PromptFileMapper promptFileMapper;


    /**
     * 查询所有的vip标准信息
     *
     * @param user
     * @return
     */
    public List<VipStandards> getVipStandardsList(User user) {
        try {
            List<VipStandards> vipStandardsList = new ArrayList<>();
            String vipStandardsListResult = (String) redisService.get(USER_VIP_STANDARDS);

            if (StringUtil.isBlank(vipStandardsListResult)) {
                LambdaQueryWrapper<VipStandards> lrw = new LambdaQueryWrapper();
                lrw.isNotNull(VipStandards::getId);
                vipStandardsList = vipStandardsMapper.selectList(lrw);

                redisService.set(USER_VIP_STANDARDS, JsonUtils.writeToString(vipStandardsList));
            } else {
                vipStandardsList = JsonUtils.writeToList(vipStandardsListResult, VipStandards.class);
            }
            return vipStandardsList;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 为移动端做兼容，后续删除
     *
     * @param user
     * @return
     */
    public OldUserLumens getOldUserLumens(User user) {
        OldUserLumens oldUserLumens = new OldUserLumens();

        UserLumens userLumens = getUserLumens(user);
        if (!Objects.isNull(userLumens)) {
            BeanUtils.copyProperties(userLumens, oldUserLumens, "rechargeLumens", "leftRechargeLumens");
            oldUserLumens.setRechargeLumens(userLumens.getRechargeLumens() + userLumens.getGiftLumens());
            oldUserLumens.setLeftRechargeLumens(userLumens.getLeftRechargeLumens() + userLumens.getLeftGiftLumens());
        }

        return oldUserLumens;
    }

    /**
     * 查询当前用户的点数信息
     *
     * @return
     */
    public UserLumens getUserLumens(User user) {
        try {
            UserLumens userLumens = new UserLumens();
            Integer dailyLumens = user.getDailyLumens() == null ? 10 : user.getDailyLumens();
            Integer userDailyLumens = user.getUseDailyLumens() == null ? 0 : user.getUseDailyLumens();
            Long dailyLumensTime = user.getDailyLumensTime();

            //查询每日免费点数点数情况
            userLumens.setDailyLumens(dailyLumens);
            userLumens.setLeftDailyLumens(dailyLumens - userDailyLumens);
            if (dailyLumensTime != null) {
                LocalDateTime localDateTime = DateUtils.convertUtcToBeijingLocalDateTime(dailyLumensTime);
                userLumens.setDailyLumensTime(localDateTime);
            }

            //获取用户的点数情况
            Integer userRechargeTotalLumens = (Integer) redisService.getDataFromHash(USER_RECHARGE_TOTAL_LUMENS, user.getLoginName());
            Integer userRechargeUseLumens = (Integer) redisService.getDataFromHash(USER_RECHARGE_USE_LUMENS, user.getLoginName());
            Integer userVipTotalLumens = (Integer) redisService.getDataFromHash(USER_VIP_TOTAL_LUMENS, user.getLoginName());
            Integer userVipUseLumens = (Integer) redisService.getDataFromHash(USER_VIP_USE_LUMENS, user.getLoginName());
            Integer userGiftTotalLumens = (Integer) redisService.getDataFromHash(USER_GIFT_TOTAL_LUMENS, user.getLoginName());
            Integer userGiftUseLumens = (Integer) redisService.getDataFromHash(USER_GIFT_USE_LUMENS, user.getLoginName());

            //如果有一个点数为空，则查数据库，重新进行统计
            if (Objects.isNull(userRechargeTotalLumens) || Objects.isNull(userRechargeUseLumens)
                    || Objects.isNull(userVipTotalLumens) || Objects.isNull(userVipUseLumens)
                    || Objects.isNull(userGiftTotalLumens) || Objects.isNull(userGiftUseLumens)) {
                Long nowTimeLong = System.currentTimeMillis() / 1000;
                //查询会员有效点数和充值有效点数
                LambdaQueryWrapper<PayLumenRecord> payRecordlqw = new LambdaQueryWrapper();
                payRecordlqw.eq(PayLumenRecord::getLoginName, user.getLoginName());
                payRecordlqw.eq(PayLumenRecord::getInvalid, 0);
                payRecordlqw.le(PayLumenRecord::getCurrentPeriodStart, nowTimeLong);
                payRecordlqw.gt(PayLumenRecord::getCurrentPeriodEnd, nowTimeLong);
                payRecordlqw.orderByDesc(PayLumenRecord::getType);
                payRecordlqw.orderByAsc(PayLumenRecord::getCreateTime);
                List<PayLumenRecord> payLumenRecordList = payLumenRecordMapper.selectList(payRecordlqw);

                Map<Integer, Integer> lumenMap = new HashMap<>();
                Map<Integer, Integer> leftLumenMap = new HashMap<>();
                if (!CollectionUtils.isEmpty(payLumenRecordList)) {
                    // 分组并求lumen的和
                    lumenMap = payLumenRecordList.stream()
                            .collect(Collectors.groupingBy(
                                    PayLumenRecord::getType,                // 按 type 分组
                                    Collectors.summingInt(PayLumenRecord::getLumenQty) // 计算 lumen_qty 的总和
                            ));

                    // 分组并求剩余lumen的和
                    leftLumenMap = payLumenRecordList.stream()
                            .collect(Collectors.groupingBy(
                                    PayLumenRecord::getType,                // 按 type 分组
                                    Collectors.summingInt(PayLumenRecord::getLumenLeftQty) // 计算 lumen_left_qty 的总和
                            ));
                }

                // 获取充值和赠送的点数
                int rechargeLumens = !Objects.isNull(lumenMap.get(LumenType.recharge.getValue())) ? lumenMap.get(LumenType.recharge.getValue()) : 0;
                int giftLumens = !Objects.isNull(lumenMap.get(LumenType.gift.getValue())) ? lumenMap.get(LumenType.gift.getValue()) : 0;
                int vipLumens = !Objects.isNull(lumenMap.get(LumenType.vip.getValue())) ? lumenMap.get(LumenType.vip.getValue()) : 0;

                // 获取充值和赠送的剩余点数
                int leftRechargeLumens = !Objects.isNull(leftLumenMap.get(LumenType.recharge.getValue())) ? leftLumenMap.get(LumenType.recharge.getValue()) : 0;
                int leftGiftLumens = !Objects.isNull(leftLumenMap.get(LumenType.gift.getValue())) ? leftLumenMap.get(LumenType.gift.getValue()) : 0;
                int leftVipLumens = !Objects.isNull(leftLumenMap.get(LumenType.vip.getValue())) ? leftLumenMap.get(LumenType.vip.getValue()) : 0;

                //设置会员和充值标准点数
                userLumens.setRechargeLumens(rechargeLumens);
                userLumens.setVipLumens(vipLumens);
                userLumens.setGiftLumens(giftLumens);
                //设置会员和剩余标准点数
                userLumens.setLeftRechargeLumens(leftRechargeLumens);
                userLumens.setLeftVipLumens(leftVipLumens);
                userLumens.setLeftGiftLumens(leftGiftLumens);
                //设置剩余点数总数
                userLumens.setLeftTotalLumens(userLumens.getLeftDailyLumens() + leftRechargeLumens + leftVipLumens + leftGiftLumens);

                redisService.putDataToHash(USER_RECHARGE_TOTAL_LUMENS, user.getLoginName(), userLumens.getRechargeLumens());
                redisService.putDataToHash(USER_RECHARGE_USE_LUMENS, user.getLoginName(), userLumens.getRechargeLumens() - userLumens.getLeftRechargeLumens());
                redisService.putDataToHash(USER_VIP_TOTAL_LUMENS, user.getLoginName(), userLumens.getVipLumens());
                redisService.putDataToHash(USER_VIP_USE_LUMENS, user.getLoginName(), userLumens.getVipLumens() - userLumens.getLeftVipLumens());
                redisService.putDataToHash(USER_GIFT_TOTAL_LUMENS, user.getLoginName(), userLumens.getGiftLumens());
                redisService.putDataToHash(USER_GIFT_USE_LUMENS, user.getLoginName(), userLumens.getGiftLumens() - userLumens.getLeftGiftLumens());

            } else {
                userLumens.setRechargeLumens(userRechargeTotalLumens);
                userLumens.setVipLumens(userVipTotalLumens);
                userLumens.setGiftLumens(userGiftTotalLumens);
                //设置会员和剩余标准点数
                userLumens.setLeftRechargeLumens(userRechargeTotalLumens - userRechargeUseLumens);
                userLumens.setLeftVipLumens(userVipTotalLumens - userVipUseLumens);
                userLumens.setLeftGiftLumens(userGiftTotalLumens - userGiftUseLumens);
                //设置剩余点数总数
                userLumens.setLeftTotalLumens(userLumens.getLeftDailyLumens() + userLumens.getLeftRechargeLumens() + userLumens.getLeftVipLumens() + userLumens.getLeftGiftLumens());
            }

            return userLumens;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public Integer getUserRechargeLumens(User user) {
        Long nowTimeLong = System.currentTimeMillis() / 1000;

        //查询会员有效点数和充值有效点数
        LambdaQueryWrapper<PayLumenRecord> payRecordlqw = new LambdaQueryWrapper();
        payRecordlqw.eq(PayLumenRecord::getLoginName, user.getLoginName());
        payRecordlqw.eq(PayLumenRecord::getInvalid, 0);
        payRecordlqw.eq(PayLumenRecord::getType, LumenType.recharge.getValue());
        payRecordlqw.le(PayLumenRecord::getCurrentPeriodStart, nowTimeLong);
        payRecordlqw.gt(PayLumenRecord::getCurrentPeriodEnd, nowTimeLong);
        List<PayLumenRecord> payLumenRecordList = payLumenRecordMapper.selectList(payRecordlqw);

        Integer rechargeLumen = 0;
        if (!CollectionUtils.isEmpty(payLumenRecordList)) {
            for (PayLumenRecord payLumenRecord : payLumenRecordList) {
                rechargeLumen += payLumenRecord.getLumenQty();
            }

            //由于用户充值了lumen币，所以需要清除redis缓存
            resettingPersonalLumens(user.getLoginName());
        }

        return rechargeLumen;
    }

    public User dealUserVipStandards(User user) {
        if (Objects.isNull(user)) {
            return user;
        }

        // 当天开始时间戳（秒）
        Long nowTimeStartOfDaySecond = LocalDate.now(ZoneOffset.UTC).atStartOfDay(ZoneOffset.UTC).toEpochSecond();
        Long nowTimeLong = System.currentTimeMillis() / 1000;

        //是否有数据库变更标记
        int count = 0;
        //如果用户每日免费为空，则赠送10点  如果用户每日免费不为空，则判断时间是否一致，
        // 不一致则更新每日免费点数使用量和时间
        if (user.getDailyLumensTime() == null || !user.getDailyLumensTime().equals(nowTimeStartOfDaySecond)) {
            LambdaUpdateWrapper<User> luw = new LambdaUpdateWrapper();

            luw.set(User::getDailyLumensTime, nowTimeStartOfDaySecond);
            luw.set(User::getDailyLumens, 0);
            luw.set(User::getUseDailyLumens, 0);

            user.setDailyLumensTime(nowTimeStartOfDaySecond);
            user.setDailyLumens(0);
            user.setUseDailyLumens(0);
            user.setDailyUpdate(Boolean.TRUE);

            lumenTaskService.resetTask(user);

//            JsonNode userConfig = user.getUserConfig();
//            if (userConfig != null && userConfig.isObject()) {
//                ObjectNode objectNode = (ObjectNode) userConfig;
//                objectNode.put("relaxLimit", false);
//                user.setUserConfig(objectNode);
//                luw.set(User::getUserConfig, objectNode);
//            }

            luw.set(User::getUpdateTime, LocalDateTime.now());
            luw.eq(User::getId, user.getId());
            userMapper.update(null, luw);
            log.info("修改用户:{} 每天的lumen信息:{}", user.getLoginName(), luw.getCustomSqlSegment());
        }

        //判断用户vip是否已经过期，过期了，则置为普通用户
        if (!VipType.basic.getValue().equals(user.getVipType())
                || !Objects.isNull(user.getVipEndTime())
                || !Objects.isNull(user.getVipBeginTime())
                || !Objects.isNull(user.getPriceInterval())) {
            LambdaUpdateWrapper<User> luwNew = new LambdaUpdateWrapper<>();
            String srcVipType = user.getVipType();
            Long srcVipEndTime = user.getVipEndTime();
            SubscriptionCurrent logicValidHighSubscriptionsFromDb = subscriptionCurrentService.getLogicValidHighSubscriptionsFromDb(user.getId());
            if (!VipType.basic.getValue().equals(logicValidHighSubscriptionsFromDb.getPlanLevel())
                    && (!Objects.equals(logicValidHighSubscriptionsFromDb.getPlanLevel(), srcVipType) || !Objects.equals(logicValidHighSubscriptionsFromDb.getVipEndTime(), srcVipEndTime))) {
                // logic 不是basic 并且user的vipType 不等于 logic的vipType 或者 user的vipEndTime 不等于 logic的vipEndTime
                luwNew.set(logicValidHighSubscriptionsFromDb.getVipEndTime() != null, User::getVipEndTime, logicValidHighSubscriptionsFromDb.getVipEndTime());
                luwNew.set(User::getVipType, logicValidHighSubscriptionsFromDb.getPlanLevel());
                luwNew.set(User::getPriceInterval, logicValidHighSubscriptionsFromDb.getPriceInterval());
                luwNew.set(User::getUpdateTime, LocalDateTime.now());

                user.setVipType(logicValidHighSubscriptionsFromDb.getPlanLevel());
                user.setVipEndTime(logicValidHighSubscriptionsFromDb.getVipEndTime());
                luwNew.eq(User::getId, user.getId())
                        .eq(User::getVipEndTime, srcVipEndTime)
                        .eq(User::getVipType, srcVipType);

                userMapper.update(null, luwNew);
                log.info("修改用户vip权限1:{} 会员或者vip,sql:{}", user.getLoginName(), luwNew.getCustomSqlSegment());
            } else if (VipType.basic.getValue().equals(logicValidHighSubscriptionsFromDb.getPlanLevel())
                    && !Objects.equals(srcVipType, VipType.basic.getValue())) {
                // logic是basic 并且user的vipType 不等于basic
                luwNew.set(User::getVipType, VipType.basic.getValue());

                user.setVipType(VipType.basic.getValue());
                luwNew.eq(User::getId, user.getId())
                        .eq(User::getVipEndTime, srcVipEndTime)
                        .eq(User::getVipType, srcVipType);

                userMapper.update(null, luwNew);
                log.info("修改用户vip权限2:{} 会员或者vip,sql:{}", user.getLoginName(), luwNew.getCustomSqlSegment());
            }
        }

        return user;
    }

    /**
     * 检查两个日期是否为同一天（比较格式化后的日期字符串）。
     * 处理时区差异问题。
     */
    private boolean isSameDate(String dateTime, LocalDate currentDate) {
        try {
            LocalDate parsedDate = LocalDate.parse(dateTime, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            return parsedDate.isEqual(currentDate);
        } catch (DateTimeParseException e) {
            return false; // 无效格式，默认认为不是同一天
        }
    }

    public Boolean judgeUserFastCreate(String modelId, User user, int batchSize, Boolean highPixels, OriginCreate originCreate, String fileUrl, Double scale) {
        RLock lock = redissonClient.getLock(FAST_CREATE_LOCK_PREFIX + user.getLoginName());
        try {
            //模型和用户信息不能为空
            if (StringUtil.isBlank(modelId) || Objects.isNull(user)) {
                throw new LogicException(LogicErrorCode.NOT_MODEL_SUPPORT);
            }

            //获取用户剩余的lumen点（预判）
            int userSurplusLumens = getUserSurplusLumens(user);
            if (userSurplusLumens <= 0) {
                return false;
            }
            return judgeLumenEnoughByToolType(user.getLoginName(), userSurplusLumens, modelId, batchSize, highPixels, originCreate, fileUrl, scale);
        } catch (Exception e) {
            log.error("checkUserFastCreate", e);
            throw new RuntimeException("checkUserFastCreate Failed");
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    private boolean judgeLumenEnoughByToolType(String loginName, int userSurplusLumens, String modelId, int batchSize, Boolean highPixels,
                                               OriginCreate originCreate, String fileUrl, Double scale) {
        if (Objects.equals(OriginCreate.hiresFix, originCreate)) {
            //超分按像素计算
            return judgeByPixel(loginName, userSurplusLumens, fileUrl, scale);
        } else {
            //其余按张数计算
            return judgeCreate(userSurplusLumens, modelId, batchSize, highPixels);
        }
    }

    private boolean judgeByPixel(String loginName, int userSurplusLumens, String fileUrl, Double scale) {
        if (StringUtils.isBlank(fileUrl)) {
            return false;
        }

        PromptFile promptFile = promptFileMapper.selectOne(new LambdaQueryWrapper<>(PromptFile.class)
                .eq(PromptFile::getLoginName, loginName)
                .eq(PromptFile::getFileUrl, fileUrl));
        if (Objects.isNull(promptFile)) {
            return false;
        }

        return genService.calculateCostLumenByPixel((int) DoubleMathUtils.mul(promptFile.getWidth(), scale), (int) DoubleMathUtils.mul(promptFile.getHeight(), scale)) <= userSurplusLumens;
    }

    private boolean judgeCreate(int userSurplusLumens, String modelId, int batchSize, Boolean highPixels) {
        //判断模型为flux-dev 模型
        if (fluxdevModelId.equals(modelId)) {
            return userSurplusLumens >= (12 * batchSize) ? Boolean.TRUE : Boolean.FALSE;
        } else if (highPixels) {
            return userSurplusLumens >= (10 * batchSize) ? Boolean.TRUE : Boolean.FALSE;
        } else {
            return userSurplusLumens >= batchSize ? Boolean.TRUE : Boolean.FALSE;
        }
    }


    public Boolean judgeUserOperatePermission(User user, int lumenCost) {
        RLock lock = redissonClient.getLock(OPERATE_PERMISSION_LOCK_PREFIX + user.getLoginName());
        try {
            //获取用户剩余的lumen点（预判）
            int userSurplusLumens = getUserSurplusLumens(user);
            return userSurplusLumens >= lumenCost ? Boolean.TRUE : Boolean.FALSE;
        } catch (Exception e) {
            log.error("checkUserOperatePermission", e);
            throw new RuntimeException("checkUserOperatePermission Failed");
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    public int getUserSurplusLumens(User user) {
        Integer userRechargeTotalLumens = (Integer) redisService.getDataFromHash(USER_RECHARGE_TOTAL_LUMENS, user.getLoginName());
        Integer userRechargeUseLumens = (Integer) redisService.getDataFromHash(USER_RECHARGE_USE_LUMENS, user.getLoginName());
        Integer userVipTotalLumens = (Integer) redisService.getDataFromHash(USER_VIP_TOTAL_LUMENS, user.getLoginName());
        Integer userVipUseLumens = (Integer) redisService.getDataFromHash(USER_VIP_USE_LUMENS, user.getLoginName());
        Integer userGiftTotalLumens = (Integer) redisService.getDataFromHash(USER_GIFT_TOTAL_LUMENS, user.getLoginName());
        Integer userGiftUseLumens = (Integer) redisService.getDataFromHash(USER_GIFT_USE_LUMENS, user.getLoginName());

        /**
         * 如果reids中的数据为空，则从数据库中取出
         */
        if (Objects.isNull(userRechargeTotalLumens) || Objects.isNull(userRechargeUseLumens)
                || Objects.isNull(userVipTotalLumens) || Objects.isNull(userVipUseLumens)
                || Objects.isNull(userGiftTotalLumens) || Objects.isNull(userGiftUseLumens)) {
            UserLumens userLumens = getUserLumens(user);

            userRechargeTotalLumens = userLumens.getRechargeLumens();
            userRechargeUseLumens = userLumens.getRechargeLumens() - userLumens.getLeftRechargeLumens();
            userVipTotalLumens = userLumens.getVipLumens();
            userVipUseLumens = userLumens.getVipLumens() - userLumens.getLeftVipLumens();
            userGiftTotalLumens = userLumens.getGiftLumens();
            userGiftUseLumens = userLumens.getGiftLumens() - userLumens.getLeftGiftLumens();
        }

        Integer notFinishTaskLumens = (Integer) redisService.getDataFromHash(USER_NOT_FINISH_TASK_LUMENS, user.getLoginName());
        notFinishTaskLumens = !Objects.isNull(notFinishTaskLumens) ? notFinishTaskLumens : 0;

        Integer dailyLumens = user.getDailyLumens() == 0 ? 0 : user.getDailyLumens();
        Integer useDailyLumens = user.getUseDailyLumens() == 0 ? 0 : user.getUseDailyLumens();


        //计算剩余的点数
        int userSurplusLumens = (userRechargeTotalLumens + userVipTotalLumens + userGiftTotalLumens + dailyLumens) -
                (userRechargeUseLumens + userVipUseLumens + userGiftUseLumens + useDailyLumens + notFinishTaskLumens);

        return userSurplusLumens;
    }

    /**
     * 更新对应的任务信息和用户信息,以及扣点
     *
     * @param markId
     * @param promptId
     * @param userLoginName
     * @param batchImgSize
     */
    public void updateMessageByMarkId(String markId, String promptId, String userLoginName, int batchImgSize, int efficientImgNum, int highEfficientCost) throws IOException {
        //查询用户信息
        LambdaUpdateWrapper<User> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(User::getLoginName, userLoginName);
        User user = userMapper.selectOne(lambdaUpdateWrapper);

        //查询生图任务
        LambdaQueryWrapper<PromptRecord> lqw = new LambdaQueryWrapper<PromptRecord>();
        lqw.eq(PromptRecord::getMarkId, markId);
        lqw.eq(PromptRecord::getLoginName, userLoginName);
        PromptRecord promptRecord = promptRecordMapper.selectOne(lqw);

        if (!Objects.isNull(promptRecord) && !Objects.isNull(user)) {

            GenGenericPara genGenericPara = JsonUtils.fromJsonNode(promptRecord.getGenInfo(), GenGenericPara.class);

            //fastHour任务,且是会员则刷新未完成的任务信息
            if (promptRecord.getFastHour() && !VipType.basic.getValue().equals(user.getVipType())) {
                genService.dealNotFinishTash(user.getLoginName());
            }

            int costTargetLumens = 0;

            //dev模型为12点数，非dev模型为1点数，removeBackground均为1点
            if (OriginCreate.removeBackground.getValue().equals(promptRecord.getOriginCreate()) || OriginCreate.enlargeImage.getValue().equals(promptRecord.getOriginCreate())) {
                costTargetLumens = efficientImgNum;
            } else {
                if (fluxdevModelId.equals(promptRecord.getModelId())) {
                    costTargetLumens = highEfficientCost * 12;
                } else if (!Objects.isNull(genGenericPara.getHighPixels()) && genGenericPara.getHighPixels()
                        && OriginCreate.create.getValue().equals(promptRecord.getOriginCreate())) {
                    costTargetLumens = efficientImgNum * 10;
                } else {
                    costTargetLumens = highEfficientCost;
                }
            }


            //更新任务的点数消耗和生图结束时间和更新时间
            LambdaUpdateWrapper<PromptRecord> luw = new LambdaUpdateWrapper<PromptRecord>();
            luw.eq(PromptRecord::getMarkId, markId);
            luw.eq(PromptRecord::getLoginName, userLoginName);
            luw.set(PromptRecord::getGenEndTime, LocalDateTime.now());
            luw.set(PromptRecord::getUpdateTime, LocalDateTime.now());
            luw.set(PromptRecord::getCostLumens, costTargetLumens);
            promptRecordMapper.update(null, luw);

            //如果是快速生图则对lumens 点数进行更新
            if (Boolean.TRUE.equals(promptRecord.getFastHour())) {
                dealUserLumensCost(costTargetLumens, markId, batchImgSize, user);
            } else {
                //统计relax生图数量
                redisService.incrementHashValue(USER_TODAY_RELAX_CREATE_IMG_NUMS, userLoginName, batchImgSize);
            }
        }
    }

    public void dealUserLumensCost(int costTargetLumens, String markId, int batchImgSize, User user) {
        //扣点顺序依次为 每天免费点数，会员vip点数，充值点数
        int remainingLumens = costTargetLumens;

        //对扣点加上分布式锁
        RLock lock = redissonClient.getLock("dealLumensAfterCreate:" + user.getLoginName());
        try {
            lock.lock();

            int surplusDailyLumens = user.getDailyLumens() - user.getUseDailyLumens();
            // 1. 扣除免费点数
            if ((surplusDailyLumens) > 0) {
                int deducted = Math.min(surplusDailyLumens, remainingLumens);
                user.setUseDailyLumens(user.getUseDailyLumens() + deducted);
                remainingLumens -= deducted;
            }

            //2. 如果免费点数不够,则扣除其他点数
            if (remainingLumens > 0) {
                remainingLumens = dealNotDailyFreeLumens(remainingLumens, user);
            }
        } catch (Exception e) {
            log.error("用户：{} 对任务：{} 扣点报错", user.getLoginName(), markId, e);
            throw new ServerInternalException("Reduce lumens Failed");
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

        //如果点数没有完全扣完，则打印错误日志
        if (!(remainingLumens == 0)) {
            log.error("用户:{},生图任务:{},扣点数有误", user.getLoginName(), markId);
        }

        user.setUpdateTime(LocalDateTime.now());
        user.setTotalImgNum(!Objects.isNull(user.getTotalImgNum()) ? (user.getTotalImgNum() + batchImgSize) : batchImgSize);
        userMapper.updateById(user);
    }

    public int dealNotDailyFreeLumens(int remainingLumens, User user) {
        //数据发生变更的PayLumenRecord
        List<PayLumenRecord> updatePayLumenRecordList = new ArrayList<>();
        List<LogLumenCost> logLumenCostList = new ArrayList<>();

        Long nowTimeLong = System.currentTimeMillis() / 1000;
        //查询会员有效点数和充值有效点数
        LambdaQueryWrapper<PayLumenRecord> payRecordlqw = new LambdaQueryWrapper();
        payRecordlqw.eq(PayLumenRecord::getLoginName, user.getLoginName());
        payRecordlqw.eq(PayLumenRecord::getInvalid, 0);
        payRecordlqw.gt(PayLumenRecord::getLumenLeftQty, 0);
        payRecordlqw.le(PayLumenRecord::getCurrentPeriodStart, nowTimeLong);
        payRecordlqw.gt(PayLumenRecord::getCurrentPeriodEnd, nowTimeLong);
        payRecordlqw.orderByDesc(PayLumenRecord::getType);
        payRecordlqw.orderByAsc(PayLumenRecord::getCreateTime);
        payRecordlqw.last("LIMIT 20");
        List<PayLumenRecord> payLumenRecordList = payLumenRecordMapper.selectList(payRecordlqw);

        if (!CollectionUtils.isEmpty(payLumenRecordList)) {
            Integer vipLumenUse = 0;
            Integer rechargeLumenUse = 0;
            Integer giftLumenUse = 0;

            for (PayLumenRecord record : payLumenRecordList) {
                if (remainingLumens <= 0) {
                    // 如果 remainingLumens 已经扣除完毕，提前结束
                    break;
                }

                int lumenLeftQty = record.getLumenLeftQty();
                int lumenCost = 0;
                int lumenLeft = 0;

                if (lumenLeftQty >= remainingLumens) {
                    // 当前条目足够扣除 remainingLumens
                    //剩余点数
                    lumenLeft = lumenLeftQty - remainingLumens;
                    record.setLumenLeftQty(lumenLeft);
                    //消耗点数
                    lumenCost = remainingLumens;
                    remainingLumens = 0; // 扣除完毕，remainingLumens 置为 0
                } else {
                    // 当前条目不足以扣除 remainingLumens，扣光当前条目
                    remainingLumens -= lumenLeftQty;
                    //消耗点数
                    lumenCost = lumenLeftQty;
                    //剩余点数
                    lumenLeft = 0;
                    record.setLumenLeftQty(lumenLeft);
                }

                record.setUpdateTime(LocalDateTime.now());
                updatePayLumenRecordList.add(record);

                //组装消费的数据
                LogLumenCost logLumenCost = new LogLumenCost();
                logLumenCost.setUserId(user.getId());
                logLumenCost.setLoginName(user.getLoginName());
                logLumenCost.setPayLumenRecordId(record.getId());
                logLumenCost.setType(record.getType());
                logLumenCost.setLumenBefore(record.getLumenLeftQty() + lumenCost);
                logLumenCost.setLumenCost(lumenCost);
                logLumenCost.setLumenLeft(record.getLumenLeftQty());
                logLumenCost.setLogicPeriodEnd(record.getLogicPeriodEnd());
                logLumenCost.setLogicPeriodStart(record.getLogicPeriodStart());
                logLumenCost.setCreateTime(LocalDateTime.now());
                logLumenCostList.add(logLumenCost);

                //判断类型
                switch (record.getType()) {
                    case 1:
                        rechargeLumenUse += lumenCost;
                        break;
                    case 3:
                        giftLumenUse += lumenCost;
                        break;
                    case 2:
                        vipLumenUse += lumenCost;
                        break;
                }
            }

            //批量更新lumen任务表
            if (!CollectionUtils.isEmpty(updatePayLumenRecordList)) {
                batchUpdatePayLumenRecord(updatePayLumenRecordList);
            }

            //批量插入lumen消费表
            if (!CollectionUtils.isEmpty(logLumenCostList)) {
                batchInsertlogLumenCost(logLumenCostList);
            }

            //如果使用量的充值点数大于0，则更新redis
            if (rechargeLumenUse > 0) {
                redisService.incrementFieldInHash(USER_RECHARGE_USE_LUMENS, user.getLoginName(), Long.valueOf(rechargeLumenUse));
            }

            //如果使用量的vip点数大于0，则更新redis
            if (vipLumenUse > 0) {
                redisService.incrementFieldInHash(USER_VIP_USE_LUMENS, user.getLoginName(), Long.valueOf(vipLumenUse));
            }

            //如果使用量的gift点数大于0，则更新redis
            if (giftLumenUse > 0) {
                redisService.incrementFieldInHash(USER_GIFT_USE_LUMENS, user.getLoginName(), Long.valueOf(giftLumenUse));
            }

        }

        return remainingLumens;
    }

    //批量更新lumen任务表
    public void batchUpdatePayLumenRecord(List<PayLumenRecord> updatePayLumenRecordList) {
        payLumenRecordService.updateBatchById(updatePayLumenRecordList);
    }

    //批量插入lumen消费表
    public void batchInsertlogLumenCost(List<LogLumenCost> logLumenCostList) {
        mongoTemplate.insertAll(logLumenCostList);
    }


    public void resettingPersonalLumens(String loginName) {
        if (StringUtil.isBlank(loginName)) {
            return;
        }

        log.info("开始重置用户:{} 有效的点数和使用的点数", loginName);
        try {
            //删除用户有效的点数
            redisService.deleteFieldFromHash(USER_RECHARGE_TOTAL_LUMENS, loginName);
            redisService.deleteFieldFromHash(USER_RECHARGE_USE_LUMENS, loginName);
            redisService.deleteFieldFromHash(USER_GIFT_TOTAL_LUMENS, loginName);
            //删除用户使用的点数
            redisService.deleteFieldFromHash(USER_VIP_TOTAL_LUMENS, loginName);
            redisService.deleteFieldFromHash(USER_VIP_USE_LUMENS, loginName);
            redisService.deleteFieldFromHash(USER_GIFT_USE_LUMENS, loginName);
            log.info("结束重置用户:{} 有效的点数和使用的点数", loginName);
        } catch (Exception e) {
            log.error("重置用户:{} 有效的点数和使用的点数失败", loginName, e);
        }
    }

    public Map<String, Object> getRealUserVipInfo(Long id, String platform) {
        SubscriptionCurrent validSubscriptionsFromDb = subscriptionCurrentService.getValidHighSubscriptionsFromDb(id, platform);

        // 构建结果 Map
        Map<String, Object> result = new HashMap<>();
        if (validSubscriptionsFromDb != null) {
            result.put("vipType", validSubscriptionsFromDb.getPlanLevel());
            result.put("priceInterval", validSubscriptionsFromDb.getPriceInterval());
        }

        return result;
    }

    //判断是否可以试用
    public Boolean canTrail(Long userId) {
        String cacheKey = LogicConstants.BUY_PLAN_USER;
        String hashKey = userId.toString();

        // 先从 Redis 获取
        Boolean value = redisService.hasHashField(cacheKey, hashKey);

        if (!value) {
            // 缓存未命中，从数据库查询用户是否买过订阅计划，只查询一条即可
            LambdaQueryWrapper<SubscriptionCurrent> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SubscriptionCurrent::getUserId, userId)
                    .ne(SubscriptionCurrent::getVipPlatform, VipPlatform.GIFT.getPlatformName())
                    .last("LIMIT 1");
            SubscriptionCurrent subscription = subscriptionCurrentService.getOne(queryWrapper);
            if (subscription == null) {
                value = true;
            } else {
                value = false;
                redisService.putDataToHash(cacheKey, hashKey, false);
            }
        } else {
            // 缓存命中，直接返回结果
            value = false;
        }

        return value;
    }


    public Boolean canFirstGift(Long id) {
        return !payLumenRecordService.hasPurchasedLumen(id);
    }

    public void clearTrialFirst(String suerName) {
        String userId = applicationContext.getBean(UserService.class).queryUserIdByName(suerName);
        if (userId != null) {
            redisService.deleteFieldFromHash(LogicConstants.BUY_PLAN_USER, userId);
            redisService.deleteFieldFromHash("user:first:lumen_flag", userId);
        }

    }
}
