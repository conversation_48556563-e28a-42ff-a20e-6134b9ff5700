package com.lx.pl.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lx.pl.client.MidjourneyApiClient;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.logging.HttpLoggingInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import retrofit2.Retrofit;
import retrofit2.converter.jackson.JacksonConverterFactory;

import java.util.concurrent.TimeUnit;

/**
 * Midjourney Retrofit配置
 * 
 * <AUTHOR>
 */
@Slf4j
@Configuration
public class MidjourneyRetrofitConfig {
    
    @Autowired
    private MidjourneyConfig midjourneyConfig;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    /**
     * 配置OkHttpClient
     */
    @Bean("midjourneyOkHttpClient")
    public OkHttpClient midjourneyOkHttpClient() {
        // 日志拦截器
        HttpLoggingInterceptor loggingInterceptor = new HttpLoggingInterceptor(log::info);
        loggingInterceptor.setLevel(HttpLoggingInterceptor.Level.BODY);
        
        return new OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(60, TimeUnit.SECONDS)
                .writeTimeout(60, TimeUnit.SECONDS)
                .addInterceptor(loggingInterceptor)
                .retryOnConnectionFailure(true)
                .build();
    }
    
    /**
     * 配置Retrofit
     */
    @Bean("midjourneyRetrofit")
    public Retrofit midjourneyRetrofit() {
        return new Retrofit.Builder()
                .baseUrl(midjourneyConfig.getBaseUrl())
                .client(midjourneyOkHttpClient())
                .addConverterFactory(JacksonConverterFactory.create(objectMapper))
                .build();
    }
    
    /**
     * 创建Midjourney API客户端
     */
    @Bean
    public MidjourneyApiClient midjourneyApiClient() {
        return midjourneyRetrofit().create(MidjourneyApiClient.class);
    }
}
