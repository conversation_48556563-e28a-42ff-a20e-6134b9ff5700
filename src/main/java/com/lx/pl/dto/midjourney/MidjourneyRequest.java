package com.lx.pl.dto.midjourney;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * Midjourney API请求基础类
 *
 * <AUTHOR>
 */
@Data
public class MidjourneyRequest {

    /**
     * 回调URL
     */
    @JsonProperty("hookUrl")
    private String hookUrl;

    /**
     * 生成模式：fast/relax/turbo
     */
    @JsonProperty("mode")
    private String mode = "fast";

    /**
     * 超时时间（秒）
     */
    @JsonProperty("timeout")
    private Integer timeout = 300;

    /**
     * 是否获取四张小图
     */
    @JsonProperty("getUImages")
    private Boolean getUImages = true;

    /**
     * 是否翻译
     */
    @JsonProperty("translation")
    private Boolean translation = true;

    /**
     * Imagine请求
     */
    @Data
    public static class ImagineRequest extends MidjourneyRequest {
        /**
         * 提示词
         */
        @NotBlank(message = "提示词不能为空")
        @JsonProperty("prompt")
        private String prompt;
    }

    /**
     * Action请求（U1-U4, V1-V4等操作）
     */
    @Data
    public static class ActionRequest extends MidjourneyRequest {
        /**
         * 任务ID
         */
        @NotBlank(message = "任务ID不能为空")
        @JsonProperty("jobId")
        private String jobId;

        /**
         * 操作类型
         */
        @NotBlank(message = "操作类型不能为空")
        @JsonProperty("action")
        private String action;
    }

    /**
     * Blend请求（图像合成）
     */
    @Data
    public static class BlendRequest extends MidjourneyRequest {
        /**
         * 图像Base64数组（2-5张）
         */
        @NotNull(message = "图像数组不能为空")
        @JsonProperty("imgBase64Array")
        private List<String> imgBase64Array;

        /**
         * 图像比例：PORTRAIT/SQUARE/LANDSCAPE
         */
        @JsonProperty("dimensions")
        private String dimensions = "SQUARE";
    }

    /**
     * Describe请求（图像描述）
     */
    @Data
    public static class DescribeRequest extends MidjourneyRequest {
        /**
         * 图像Base64
         */
        @JsonProperty("base64")
        private String base64;

        /**
         * 图像URL
         */
        @JsonProperty("url")
        private String url;
    }

    /**
     * Seed请求（获取种子）
     */
    @Data
    public static class SeedRequest extends MidjourneyRequest {
        /**
         * 任务ID
         */
        @NotBlank(message = "任务ID不能为空")
        @JsonProperty("jobId")
        private String jobId;
    }

    /**
     * Inpaint请求（区域重绘）
     */
    @Data
    public static class InpaintRequest extends MidjourneyRequest {
        /**
         * 任务ID
         */
        @NotBlank(message = "任务ID不能为空")
        @JsonProperty("jobId")
        private String jobId;

        /**
         * 蒙版Base64
         */
        @NotBlank(message = "蒙版不能为空")
        @JsonProperty("mask")
        private String mask;

        /**
         * 重绘描述
         */
        @JsonProperty("prompt")
        private String prompt;
    }

    /**
     * Fetch请求（查询任务状态）
     */
    @Data
    public static class FetchRequest {
        /**
         * 任务ID
         */
        @NotBlank(message = "任务ID不能为空")
        @JsonProperty("jobId")
        private String jobId;
    }

    /**
     * Prompt检查请求
     */
    @Data
    public static class PromptCheckRequest {
        /**
         * 提示词
         */
        @NotBlank(message = "提示词不能为空")
        @JsonProperty("prompt")
        private String prompt;
    }
}
