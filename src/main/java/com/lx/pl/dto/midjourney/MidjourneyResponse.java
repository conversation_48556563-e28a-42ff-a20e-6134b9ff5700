package com.lx.pl.dto.midjourney;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * Midjourney API响应类
 *
 * <AUTHOR>
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class MidjourneyResponse {

    /**
     * 状态：SUCCESS/FAILED
     */
    @JsonProperty("status")
    private String status;

    /**
     * 消息
     */
    @JsonProperty("message")
    private String message;

    /**
     * 数据
     */
    @JsonProperty("data")
    private Object data;

    /**
     * 基础响应数据
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class BaseResponseData {
        /**
         * 任务ID
         */
        @JsonProperty("jobId")
        private String jobId;
    }

    /**
     * 任务状态响应
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TaskStatusResponse {
        /**
         * 状态：PENDING_QUEUE/ON_QUEUE/SUCCESS/FAILED
         */
        @JsonProperty("status")
        private String status;

        /**
         * 任务ID
         */
        @JsonProperty("jobId")
        private String jobId;

        /**
         * 消息
         */
        @JsonProperty("message")
        private String message;

        /**
         * 任务数据
         */
        @JsonProperty("data")
        private TaskData data;
    }

    /**
     * 任务数据
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TaskData {
        /**
         * 操作类型
         */
        @JsonProperty("actions")
        private String actions;

        /**
         * 操作类型（兼容字段）
         */
        @JsonProperty("action")
        private String action;

        /**
         * 任务ID
         */
        @JsonProperty("jobId")
        private String jobId;

        /**
         * 进度
         */
        @JsonProperty("progress")
        private String progress;

        /**
         * 提示词
         */
        @JsonProperty("prompt")
        private String prompt;

        /**
         * Discord图像URL
         */
        @JsonProperty("discordImage")
        private String discordImage;

        /**
         * CDN图像URL
         */
        @JsonProperty("cdnImage")
        private String cdnImage;

        /**
         * 图像宽度
         */
        @JsonProperty("width")
        private Integer width;

        /**
         * 图像高度
         */
        @JsonProperty("height")
        private Integer height;

        /**
         * 回调URL
         */
        @JsonProperty("hookUrl")
        private String hookUrl;

        /**
         * 可用操作
         */
        @JsonProperty("components")
        private List<String> components;

        /**
         * 种子值
         */
        @JsonProperty("seed")
        private String seed;

        /**
         * 四张小图URL
         */
        @JsonProperty("images")
        private List<String> images;

        /**
         * 消耗配额
         */
        @JsonProperty("quota")
        private Integer quota;
    }

    /**
     * 服务状态响应
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ServiceStatusResponse {
        /**
         * 状态
         */
        @JsonProperty("status")
        private String status;

        /**
         * 消息
         */
        @JsonProperty("message")
        private String message;

        /**
         * 服务状态数据
         */
        @JsonProperty("data")
        private ServiceStatusData data;
    }

    /**
     * 服务状态数据
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ServiceStatusData {
        /**
         * Fast模式状态
         */
        @JsonProperty("fast")
        private ModeStatus fast;

        /**
         * Relax模式状态
         */
        @JsonProperty("relax")
        private ModeStatus relax;

        /**
         * Turbo模式状态
         */
        @JsonProperty("turbo")
        private ModeStatus turbo;
    }

    /**
     * 模式状态
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ModeStatus {
        /**
         * 状态码
         */
        @JsonProperty("status")
        private Integer status;

        /**
         * 运行消息
         */
        @JsonProperty("runningMessage")
        private String runningMessage;

        /**
         * 平均执行时间
         */
        @JsonProperty("averageExecute")
        private Double averageExecute;
    }

    /**
     * Prompt检查响应
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class PromptCheckResponse {
        /**
         * 状态
         */
        @JsonProperty("status")
        private String status;

        /**
         * 消息
         */
        @JsonProperty("message")
        private String message;

        /**
         * 数据
         */
        @JsonProperty("data")
        private String data;
    }
}
